{"name": "@cox/core-ui8", "version": "0.0.1", "description": "A collection of reusable react components for creating intuitive experiences", "main": "dist/cjs/bundle.js", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "workspaces": ["packages/*"], "publishConfig": {"registry": "https://repo.corp.cox.com/artifactory/api/npm/cox-coxcom-npm"}, "license": "UNLICENSED", "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -c -w", "test": "jest", "test:watch": "jest --watchAll=true", "test:coverage": "jest --watchAll=false --coverage", "test:snapshots": "jest --watchAll=false -u", "format": "prettier --write .", "lint": "eslint --fix --ext .js,.jsx,.ts,.tsx ./src", "storybook": "storybook dev -p 6008", "build-storybook": "<PERSON><PERSON><PERSON> storybook-static && storybook build", "prepare": "husky", "lint-staged": "lint-staged", "tokens-build-basic": "rm -rf design-tokens/build && node design-tokens/build-tokens.mjs", "tokens-sort": "node design-tokens/sort-themes.mjs", "tokens-build-themed": "rm -rf src/assets/theme/v3 && node design-tokens/build-themed.mjs && node design-tokens/rename-classes.mjs", "tokens-rename-classes": "node design-tokens/rename-classes.mjs", "smartui": "smartui storybook ./storybook-static --config .smartui.json"}, "repository": {"type": "git", "url": "https://coxrepo.corp.cox.com/stash/projects/COXW/repos/@cox/core-ui8/browse"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "dependencies": {"@babel/core": "^7.0.0-0", "@popperjs/core": "^2.11.6", "@tokens-studio/sd-transforms": "^1.2.9", "@types/dompurify": "^3.2.0", "axios": "^1.3.2", "bootstrap": "^5.2.2", "custom-event-polyfill": "^1.0.7", "dompurify": "^3.2.6", "history": "^4.10.1", "html-react-parser": "^3.0.6", "immer": "^9.0.21", "js-cookie": "^3.0.1", "lodash": "^4.17.21", "react": "18.2.0", "react-app-polyfill": "^1.0.5", "react-bootstrap": "^2.9.1", "react-dom": "18.2.0", "react-helmet": "^6.1.0", "react-multi-carousel": "^2.8.3", "react-router": "6.6.1", "react-router-dom": "5.3.3", "react-scripts": "5.0.1", "sass": "^1.57.1", "styled-components": "4.3.1", "use-immer": "^0.9.0", "web-vitals": "^2.1.4"}, "devDependencies": {"@axe-core/react": "^4.7.3", "@babel/core": "^7.21.4", "@babel/plugin-syntax-flow": "7.14.5", "@babel/plugin-transform-react-jsx": "7.14.9", "@babel/preset-env": "^7.21.4", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.21.4", "@chromatic-com/storybook": "^3.2.6", "@craco/craco": "^7.1.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^24.0.1", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-node-resolve": "^15.0.2", "@rollup/plugin-replace": "^5.0.5", "@rollup/plugin-terser": "^0.4.0", "@rollup/plugin-typescript": "^11.1.0", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/addon-mdx-gfm": "^8.6.14", "@storybook/addon-onboarding": "^8.6.14", "@storybook/addon-webpack5-compiler-swc": "^3.0.0", "@storybook/blocks": "^8.6.14", "@storybook/manager-api": "^8.6.14", "@storybook/preset-create-react-app": "^8.6.14", "@storybook/preset-scss": "^1.0.3", "@storybook/react": "^8.6.14", "@storybook/react-webpack5": "^8.6.14", "@storybook/test": "^8.6.14", "@storybook/theming": "^8.6.14", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.5.1", "@types/google.maps": "^3.43.3", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/lodash": "4.14.168", "@types/node": "^16.18.11", "@types/react": "^18.0.33", "@types/react-bootstrap": "^0.32.32", "@types/react-dom": "^18.2.18", "@types/react-helmet": "^6.1.11", "@types/react-router-dom": "5.3.3", "@types/react-test-renderer": "^18.0.7", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@welldone-software/why-did-you-render": "^7.0.1", "babel-jest": "^29.5.0", "babel-plugin-named-exports-order": "0.0.2", "babel-preset-react-app": "^10.0.1", "clean-webpack-plugin": "^0.1.19", "dotenv": "^16.5.0", "eslint": "^8.38.0", "eslint-config-prettier": "^8.6.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-import": "2.27.5", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.12.0", "eslint-plugin-testing-library": "^5.10.0", "husky": "^9.1.4", "identity-obj-proxy": "^3.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "jest-property-matchers": "^0.1.2", "jest-styled-components": "^7.2.0", "jest-watch-typeahead": "^2.2.2", "lint-staged": "^13.3.0", "prettier": "^2.8.8", "prop-types": "^15.8.1", "react-test-renderer": "^18.2.0", "rimraf": "^4.4.1", "rollup": "^3.20.2", "rollup-plugin-dts": "^5.3.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-preserve-directives": "^0.3.1", "rollup-plugin-visualizer": "^5.12.0", "storybook": "^8.6.14", "style-dictionary": "^4.3.0", "ts-jest": "^29.2.5", "ts-jest-mock": "^1.1.41", "tslib": "^2.5.0", "typescript": "~5.3.3"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["prettier --write", "pnpm lint", "pnpm test --silent -- --watchAll=false --json --passWithNoTests -u"]}}