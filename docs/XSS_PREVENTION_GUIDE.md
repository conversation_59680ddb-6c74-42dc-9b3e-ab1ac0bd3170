# XSS Prevention Guide

This guide outlines the XSS (Cross-Site Scripting) prevention measures implemented in the UI8 component library and provides best practices for developers.

## Overview

Cross-Site Scripting (XSS) attacks occur when malicious scripts are injected into web applications. Our component library implements multiple layers of protection to prevent these attacks.

## Implemented Security Measures

### 1. URL Sanitization

All URLs used in `href` and `action` attributes are sanitized using the `sanitizeUrl` function:

```typescript
import { sanitizeUrl } from '../utils/xss-prevention-util'

// Safe usage
const safeHref = sanitizeUrl(userProvidedUrl)
```

**Protected against:**
- `javascript:` URLs
- `data:` URLs  
- `vbscript:` URLs
- Other dangerous protocols

### 2. HTML Content Sanitization

Rich text content is sanitized using DOMPurify before rendering:

```typescript
import { sanitizeRichTextHtml } from '../utils/xss-prevention-util'

// Safe HTML rendering
const safeHtml = sanitizeRichTextHtml(userContent)
```

**Features:**
- Removes dangerous script tags
- Strips event handlers (onclick, onload, etc.)
- Sanitizes URLs in href and src attributes
- Preserves safe HTML formatting

### 3. Form Action Protection

Form actions are more strictly validated to only allow HTTPS URLs:

```typescript
import { sanitizeFormAction } from '../utils/xss-prevention-util'

// Safe form action
const safeAction = sanitizeFormAction(formActionUrl)
```

## Updated Components

### BaseButton Component
- **Fixed:** Lines 93, 102, 120 - href and action attributes now sanitized
- **Protection:** URL sanitization prevents javascript: and data: URL attacks

### FooterNav Component  
- **Fixed:** Line 69 - href attributes in navigation links sanitized
- **Protection:** Prevents malicious URLs in footer navigation

### RichText Component
- **Enhanced:** HTML content sanitization with DOMPurify
- **Protection:** Comprehensive XSS prevention for rich text content

## Available Utility Functions

### `sanitizeUrl(url: string | undefined | null): string`
Sanitizes URLs for use in href attributes.

### `sanitizeFormAction(action: string | undefined | null): string`
Sanitizes form action URLs (more restrictive, HTTPS only for absolute URLs).

### `sanitizeHtml(html: string, allowedTags?: string[]): string`
Sanitizes HTML content with configurable allowed tags.

### `sanitizeRichTextHtml(html: string): string`
Sanitizes HTML for rich text components with permissive safe tags.

### `sanitizeText(text: string): string`
Encodes HTML entities in plain text.

### `sanitizeAttribute(value: string): string`
Sanitizes HTML attribute values.

### `sanitizeAnchor(anchor: string): string`
Validates and sanitizes anchor fragments.

## Best Practices for Developers

### 1. Always Sanitize User Input
```typescript
// ❌ Dangerous
<a href={userInput}>Link</a>

// ✅ Safe
<a href={sanitizeUrl(userInput)}>Link</a>
```

### 2. Use Appropriate Sanitization Functions
```typescript
// For URLs
const safeUrl = sanitizeUrl(url)

// For HTML content
const safeHtml = sanitizeRichTextHtml(htmlContent)

// For form actions
const safeAction = sanitizeFormAction(actionUrl)
```

### 3. Validate Input at Component Boundaries
Always sanitize props that come from external sources:

```typescript
const MyComponent = ({ url, content }: Props) => {
  const safeUrl = sanitizeUrl(url)
  const safeContent = sanitizeRichTextHtml(content)
  
  return (
    <div>
      <a href={safeUrl}>Link</a>
      <div>{safeContent}</div>
    </div>
  )
}
```

### 4. Be Cautious with dangerouslySetInnerHTML
Avoid using `dangerouslySetInnerHTML` directly. If you must use it, always sanitize first:

```typescript
// ❌ Dangerous
<div dangerouslySetInnerHTML={{ __html: userContent }} />

// ✅ Safe
<div dangerouslySetInnerHTML={{ __html: sanitizeRichTextHtml(userContent) }} />
```

## Testing XSS Prevention

The XSS prevention utilities include comprehensive tests. Run them with:

```bash
pnpm test src/utils/xss-prevention-util.test.ts
```

### Common Test Scenarios
- JavaScript URL injection: `javascript:alert('xss')`
- Data URL injection: `data:text/html,<script>alert('xss')</script>`
- Event handler injection: `onclick="alert('xss')"`
- Script tag injection: `<script>alert('xss')</script>`

## Security Checklist

When adding new components or modifying existing ones:

- [ ] All user-provided URLs are sanitized with `sanitizeUrl()`
- [ ] Form actions use `sanitizeFormAction()`
- [ ] HTML content uses `sanitizeRichTextHtml()` or `sanitizeHtml()`
- [ ] No direct use of `dangerouslySetInnerHTML` without sanitization
- [ ] Component props are validated and sanitized
- [ ] Tests include XSS attack scenarios

## Reporting Security Issues

If you discover a potential XSS vulnerability:

1. Do not create a public issue
2. Contact the security team directly
3. Provide detailed reproduction steps
4. Include potential impact assessment

## Additional Resources

- [OWASP XSS Prevention Cheat Sheet](https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html)
- [DOMPurify Documentation](https://github.com/cure53/DOMPurify)
- [Content Security Policy (CSP)](https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP)

## Dependencies

- **DOMPurify**: HTML sanitization library
- **html-react-parser**: Safe HTML parsing for React

These dependencies are automatically installed and configured for optimal security.
