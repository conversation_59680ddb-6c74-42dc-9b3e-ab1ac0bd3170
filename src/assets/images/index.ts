import { DAM_ICON_PATH } from '../../constants'

// FILE EXTENSIONS
const SVG = '.svg'
const PNG = '.png'
const JPG = '.jpg'
const ICO = '.ico'

/************************************** ICONS ********************************************************** */
// CAROUSEL
export const ACTIVE_PIP = `${DAM_ICON_PATH}activePip${SVG}`
export const CAROUSEL_ACTIVE_ARROW_BUTTON = `${DAM_ICON_PATH}carouselActiveButton${SVG}`
export const CAROUSEL_INACTIVE_ARROW_BUTTON = `${DAM_ICON_PATH}carouselDisabledButton${SVG}`
export const INACTIVE_PIP = `${DAM_ICON_PATH}inactivePip${SVG}`
export const PRICE_TAG = `${DAM_ICON_PATH}price-tag${SVG}`

// CHEVRON
export const CHEVRON_DOWN_RIVER_BLUE = `${DAM_ICON_PATH}chevron-down-river-blue${SVG}`
export const SM_CHEVRON_DOWN_RIVER_BLUE = `${DAM_ICON_PATH}sm-chevron-down-river-blue${SVG}`
export const CHEVRON_DOWN_LIGHT = `${DAM_ICON_PATH}chevron-down-light${SVG}`
export const CHEVRON_UP_LIGHT = `${DAM_ICON_PATH}chevron-up-light${SVG}`
export const CHEVRON_RIGHT_BLACK = `${DAM_ICON_PATH}chevron-right-black${SVG}`
export const CHEVRON_RIGHT_WHITE = `${DAM_ICON_PATH}chevron-right${SVG}`
export const CHEVRON_UP_BLACK = `${DAM_ICON_PATH}chevron-up${SVG}`
export const CHEVRON_DOWN_BLUE = `${DAM_ICON_PATH}chevron-down-blue${SVG}`
export const CHEVRON_RIGHT_BLUE = `${DAM_ICON_PATH}chevron-right-river-blue${SVG}`

// CLOSE
export const X_CIRCLE = `${DAM_ICON_PATH}times-circle${SVG}`
export const X_CIRCLE_WHITE = `${DAM_ICON_PATH}times-circle-white${SVG}`

// ERROR
export const ERROR_TRIANGLE = `${DAM_ICON_PATH}error-triangle${SVG}`

// FORM
export const CHECKMARK_WHITE = `${DAM_ICON_PATH}white-check-mark${SVG}`
export const DISABLED_QUESTION_CIRCLE = `${DAM_ICON_PATH}alert-icon${SVG}`
export const CIRCLE_CHECK_LIME_GREEN_CIRCLE = `${DAM_ICON_PATH}circle-check-lime-green${SVG}`
export const MAGNIFYING_GLASS = `${DAM_ICON_PATH}magnifying-glass${SVG}`
export const MAGNIFYING_GLASS_GREY = `${DAM_ICON_PATH}magnifying-glass-grey${SVG}`
export const QUESTION_CIRCLE = `${DAM_ICON_PATH}circle-question${SVG}`
export const QUESTION_CIRCLE_DISABLED = `${DAM_ICON_PATH}circle-question-disabled${SVG}`
export const INFO_CIRCLE_GREY = `${DAM_ICON_PATH}circle-info${SVG}`
export const PASSWORD_EYE_STRIKETHROUGH = `${DAM_ICON_PATH}eye-strikethrough${SVG}`
export const PASSWORD_EYE = `${DAM_ICON_PATH}eye${SVG}`
export const MESSAGE_SUCCESS = `${DAM_ICON_PATH}circle-check${SVG}`
export const MESSAGE_ERROR = `${DAM_ICON_PATH}red-info${SVG}`
export const MESSAGE_INFO = `${DAM_ICON_PATH}gray-info${SVG}`

// GLOBAL ASSETS
export const BELL = `${DAM_ICON_PATH}bell${SVG}`
export const BUSI_FAVICON = `${DAM_ICON_PATH}cox_business${ICO}` // MetaTagRenderer
export const COX_LOGO_OG_IMAGE = `${DAM_ICON_PATH}cox-logo-og-image${JPG}` // MetaTagRenderer
export const GLOBAL_COX_LOGO = `${DAM_ICON_PATH}cox_logo${PNG}` // Fallback
export const COX_BUSINESS_LOGO = `${DAM_ICON_PATH}cox_business_logo${PNG}`
export const GLOBAL_MAGNIFYING_GLASS = `${DAM_ICON_PATH}magnifying-glass${SVG}`
export const GLOBAL_PROFILE = `${DAM_ICON_PATH}profile${SVG}`
export const RESI_FAVICON = `${DAM_ICON_PATH}favicon${ICO}` // MetaTagRenderer

// LOCATION
export const LOCATION_PIN = `${DAM_ICON_PATH}location${SVG}`
export const LOCATION_CLOSE = `${DAM_ICON_PATH}close-btn${SVG}`

// MENU
export const HAMBURGER_MENU = `${DAM_ICON_PATH}hamburger-menu${SVG}`
export const SHOPPING_CART_BLACK = `${DAM_ICON_PATH}black-shopping-cart${SVG}`
export const CART_SKY_BLUE = `${DAM_ICON_PATH}cart-sky-blue${SVG}`

// SEARCH
export const CLOSE_X_ICON = `${DAM_ICON_PATH}x${SVG}`

// SUB NAV
export const ARROW_DOWN_ICON = `${DAM_ICON_PATH}arrow-down-large-black${SVG}`
export const ARROW_LEFT_ICON = `${DAM_ICON_PATH}arrow-left-large-black${SVG}`
export const ARROW_RIGHT_ICON = `${DAM_ICON_PATH}arrow-right-large-black${SVG}`
export const X_BLACK_ICON = `${DAM_ICON_PATH}x-black${SVG}`

// MODAL
export const CLOSE_BLUE_ICON = `${DAM_ICON_PATH}close-river-blue${SVG}`
export const CLOSE_WHITE_ICON = `${DAM_ICON_PATH}close-white${SVG}`

//BUTTON
export const PLUS = `${DAM_ICON_PATH}plus${SVG}`

// Dynamic Banner
export const CIRCLE_EXCLAMATION_ERROR = `${DAM_ICON_PATH}circle-exclamation-moderate-red${SVG}`

//list
export const CHECK_FILLED = `${DAM_ICON_PATH}check-filled${SVG}`

export const PAGE_LOADER = `${DAM_ICON_PATH}loader-gradient${SVG}` // page loader
export const PAGE_LOADER_BUSI = `${DAM_ICON_PATH}loader-gradient-busi${SVG}` // page loader

export const LARGE_VIDEO_ICON = `${DAM_ICON_PATH}video-icon-lg${SVG}`
export const MEDIUM_VIDEO_ICON = `${DAM_ICON_PATH}video-icon-md${SVG}`
export const SMALL_VIDEO_ICON = `${DAM_ICON_PATH}video-icon-xs${SVG}`

// Pagination
export const PAGINATION_FIRST_ENABLED = `${DAM_ICON_PATH}pagination-first-enabled${SVG}`
export const PAGINATION_FIRST_DISABLED = `${DAM_ICON_PATH}pagination-first-disabled${SVG}`
export const PAGINATION_PREV_ACTIVE = `${DAM_ICON_PATH}pagination-prev-active${SVG}`
export const PAGINATION_PREV_DISABLED = `${DAM_ICON_PATH}pagination-prev-disabled${SVG}`
export const PAGINATION_NEXT_ACTIVE = `${DAM_ICON_PATH}pagination-next-active${SVG}`
export const PAGINATION_NEXT_DISABLED = `${DAM_ICON_PATH}pagination-next-disabled${SVG}`
export const PAGINATION_LAST_ACTIVE = `${DAM_ICON_PATH}pagination-last-active${SVG}`
export const PAGINATION_LAST_DISABLED = `${DAM_ICON_PATH}pagination-last-disabled${SVG}`
