import styled from 'styled-components'

import { ColumnLayoutProps, LayoutBackgroundTypes, VerticalAlignment } from './types'
import { getImageSrc } from '../../../utils/local-util'

export const ColumnLayoutStyled = styled.div<ColumnLayoutProps>`
  background: ${(props) =>
    props.layoutBackground === LayoutBackgroundTypes.image
      ? `url('${getImageSrc(props.backgroundImage)}') center/ cover no-repeat`
      : props.layoutBackground === LayoutBackgroundTypes.transparent
      ? `transparent`
      : undefined};

  align-items: ${(props) =>
    props.verticalAlignment ? `${VerticalAlignment[props.verticalAlignment as keyof typeof VerticalAlignment]}` : ``};
`
