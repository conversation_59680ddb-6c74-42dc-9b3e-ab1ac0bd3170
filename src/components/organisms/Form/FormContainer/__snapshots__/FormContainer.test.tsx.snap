// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`React Form Container matches the snapshot 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="form-container fields-align-center  "
        data-testid="form-container"
      >
        <div
          class="form-main-heading align-items-center"
        >
          <div
            class="form-heading section-header-container  "
            data-testid="form-section-header"
          >
            <div
              class="eyebrow main-eyebrow"
              data-testid="form-section-header-eyebrow"
            >
              <div
                class="word-wrap rte  "
                data-testid="rich-text-container"
                id="rich-text"
              >
                Eyebrow
              </div>
               
            </div>
            <div
              class="heading main-heading"
              data-testid="form-section-header-title"
            >
              <div
                class="word-wrap rte  "
                data-testid="rich-text-container"
                id="rich-text"
              >
                Heading
              </div>
               
            </div>
            <div
              class="subheading main-description"
              data-testid="form-section-header-description"
            >
              Sub Heading
               
            </div>
          </div>
        </div>
        <form
          action="/content/usergenerated/cox-cms-react/us/en/form-container/cq-gen1679938839241/"
          id="form"
          method="post"
          novalidate=""
        >
          <div
            class="form-fields"
          />
          <div
            class="form-footer false"
          >
            <div
              class="button-group-container "
              data-testid="form-buttons-form"
              id="form-buttons-form"
            >
              <div
                data-testid="btn-group-container-form-buttons-form-0"
              >
                <div
                  class="button  undefined "
                >
                  <div
                    class="button-container"
                    data-testid="undefined-button"
                  >
                    <div
                      class="text-button "
                    >
                      <a
                        class="button-base primary responsive-size medium active"
                        href="#"
                        role="link"
                        target="_self"
                      >
                        <div
                          class="button-text"
                        >
                          SUBMIT
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="word-wrap rte disclaimer "
              data-testid="form-disclaimer-container"
              id="form-disclaimer"
            >
              disclaimer
            </div>
          </div>
        </form>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="form-container fields-align-center  "
      data-testid="form-container"
    >
      <div
        class="form-main-heading align-items-center"
      >
        <div
          class="form-heading section-header-container  "
          data-testid="form-section-header"
        >
          <div
            class="eyebrow main-eyebrow"
            data-testid="form-section-header-eyebrow"
          >
            <div
              class="word-wrap rte  "
              data-testid="rich-text-container"
              id="rich-text"
            >
              Eyebrow
            </div>
             
          </div>
          <div
            class="heading main-heading"
            data-testid="form-section-header-title"
          >
            <div
              class="word-wrap rte  "
              data-testid="rich-text-container"
              id="rich-text"
            >
              Heading
            </div>
             
          </div>
          <div
            class="subheading main-description"
            data-testid="form-section-header-description"
          >
            Sub Heading
             
          </div>
        </div>
      </div>
      <form
        action="/content/usergenerated/cox-cms-react/us/en/form-container/cq-gen1679938839241/"
        id="form"
        method="post"
        novalidate=""
      >
        <div
          class="form-fields"
        />
        <div
          class="form-footer false"
        >
          <div
            class="button-group-container "
            data-testid="form-buttons-form"
            id="form-buttons-form"
          >
            <div
              data-testid="btn-group-container-form-buttons-form-0"
            >
              <div
                class="button  undefined "
              >
                <div
                  class="button-container"
                  data-testid="undefined-button"
                >
                  <div
                    class="text-button "
                  >
                    <a
                      class="button-base primary responsive-size medium active"
                      href="#"
                      role="link"
                      target="_self"
                    >
                      <div
                        class="button-text"
                      >
                        SUBMIT
                      </div>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="word-wrap rte disclaimer "
            data-testid="form-disclaimer-container"
            id="form-disclaimer"
          >
            disclaimer
          </div>
        </div>
      </form>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
