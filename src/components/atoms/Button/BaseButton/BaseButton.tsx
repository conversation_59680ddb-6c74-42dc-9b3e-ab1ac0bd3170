import { useRef } from 'react'

import { appendHash, scrollToElementWithOffset } from '../../../../utils/helper-util'
import { sanitizeUrl, sanitizeFormAction } from '../../../../utils/xss-prevention-util'
import { AlignmentProps, ButtonNavigation, ButtonProps, ButtonStates, ButtonTypes, LinkTypes } from '../types'

const BaseButton: React.FC<Partial<ButtonProps>> = ({
  alignment = AlignmentProps.CENTER,
  buttonTypes,
  buttonStates,
  anchor,
  linkUrl = '',
  iconAltText,
  openInNewTab,
  id,
  children,
  isFormSubmit,
  customClickEvent,
  linkId,
  linkType,
  size = 'medium',
  index,
  navigation,
  customProps,
  ruleExpression
}) => {
  const tokenAnchor = (anchor as string) || (linkId as string)
  const tokenIconAltText = iconAltText as string
  const rawUrl = linkType !== LinkTypes.MODAL && linkType !== LinkTypes.CAROUSEL ? appendHash(linkUrl, tokenAnchor) : ''
  const onClickUrl = sanitizeUrl(rawUrl)
  const openUrl = openInNewTab ? '_blank' : '_self'

  const handleModalClick = () => {
    const event = new CustomEvent('showModal', {
      detail: { modalId: linkId, showModal: true, index: index, ruleExpression: ruleExpression }
    })
    window.dispatchEvent(event)
  }

  const handleGoToSlide = () => {
    const event = new CustomEvent('goToSlide', {
      detail: { slideId: tokenAnchor, index: index }
    })
    window.dispatchEvent(event)
  }

  const onClick = (event: any) => {
    if (buttonStates === ButtonStates.DISABLED) {
      event.preventDefault()
      return
    }
    if (navigation) {
      handleNavigation(navigation)
    } else {
      handleAction(event)
    }
  }

  const handleNavigation = (navigation: ButtonNavigation) => {
    if (navigation === ButtonNavigation.PREVIOUS) {
      window?.history.back()
    } else if (navigation === ButtonNavigation.NEXT) {
      window.history.forward()
    }
  }

  const handleAction = (event: MouseEvent) => {
    if (customClickEvent) {
      customClickEvent()
    } else if (linkType === LinkTypes.MODAL) {
      event.preventDefault()
      handleModalClick()
    } else if (linkType === LinkTypes.CAROUSEL) {
      handleGoToSlide()
    } else if (linkType === LinkTypes.PAGE && tokenAnchor) {
      scrollToElementWithOffset(event, linkUrl, tokenAnchor, openInNewTab)
    }
  }

  if (customProps?.setDisposition && onClickUrl) {
    const { offerId, dispositionList, onAddToCart } = customProps
    const setDispFormRef = useRef<any>(null)

    const handleSetDispFormSubmit = (event: any) => {
      event.preventDefault()
      const setDispForm = setDispFormRef?.current
      if (setDispForm?.getAttribute('data-set-disposition') === 'true') {
        setDispForm?.setAttribute('data-set-disposition', 'false')
        setDispForm?.submit()
        onAddToCart?.(dispositionList)
      }
    }
    return (
      <a
        id={id}
        role={'button'}
        href={onClickUrl}
        className={`button-base ${buttonTypes} responsive-size ${size} ${buttonStates}`}
        target={openUrl}
        onClick={handleSetDispFormSubmit}
      >
        {children}
        <form
          action={sanitizeFormAction(onClickUrl)}
          method='POST'
          className='d-none'
          ref={setDispFormRef}
          data-set-disposition='true'
          noValidate
        >
          <input type='hidden' name='bmOfferId' value={offerId} />
          <input type='hidden' name='setDisposition' value={dispositionList} />
        </form>
      </a>
    )
  } else if (onClickUrl && !customClickEvent) {
    const btnRoleTyp = linkType === LinkTypes.PAGE || LinkTypes.CAROUSEL ? 'link' : 'button'
    const buttonRole =
      buttonTypes === ButtonTypes.TERTIARY || buttonTypes === ButtonTypes.TERTIARYALT ? undefined : btnRoleTyp
    return (
      <a
        id={id}
        role={buttonRole}
        href={onClickUrl}
        className={`button-base ${buttonTypes} responsive-size ${size} ${buttonStates}`}
        target={openUrl}
        onClick={(e) => linkType !== LinkTypes.CHAT && onClick(e)}
      >
        {children}
      </a>
    )
  } else {
    const modalId = linkType === LinkTypes.MODAL ? { 'data-modal-id': linkId } : ''
    return (
      <button
        id={id}
        onClick={(e) => linkType !== LinkTypes.CHAT && onClick(e)}
        className={`button-base ${buttonTypes} responsive-size ${size} ${buttonStates} align-${alignment}`}
        type={isFormSubmit ? 'submit' : 'button'}
        aria-label={tokenIconAltText}
        {...modalId}
      >
        {children}
      </button>
    )
  }
}
export default BaseButton
