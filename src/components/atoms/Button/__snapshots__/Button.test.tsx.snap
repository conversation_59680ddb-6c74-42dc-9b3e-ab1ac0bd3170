// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Button component it does not render the button if there is no text or link 1`] = `
<div
  className="button  undefined "
/>
`;

exports[`Button component it renders the correct snapshot for a Text Button 1`] = `
<div
  className="button  undefined "
>
  <div
    className="button-container"
    data-testid="test-text-butotn-button"
  >
    <div
      className="text-button "
    >
      <a
        className="button-base primary responsive-size medium active"
        href="#"
        id="test-text-butotn"
        onClick={[Function]}
        role="link"
        target="_self"
      >
        <div
          className="button-text"
        >
          I am a button
        </div>
      </a>
    </div>
  </div>
</div>
`;

exports[`Button component it renders the correct snapshot for a Text Button Linked to Modal 1`] = `
<div
  className="button  undefined "
>
  <div
    className="button-container"
    data-testid="test-text-butotn-button"
  >
    <div
      className="text-button text-center"
    >
      <a
        className="button-base primary responsive-size medium active"
        href="#"
        id="test-text-butotn"
        onClick={[Function]}
        role="link"
        target="_self"
      >
        <div
          className="button-text"
        >
          I am a button
        </div>
      </a>
    </div>
  </div>
</div>
`;

exports[`Button component it renders the correct snapshot for a Text Button Linked to Page 1`] = `
<div
  className="button  undefined "
>
  <div
    className="button-container"
    data-testid="test-text-butotn-button"
  >
    <div
      className="text-button text-center"
    >
      <a
        className="button-base primary responsive-size medium active"
        href="/residential/home.html"
        id="test-text-butotn"
        onClick={[Function]}
        role="link"
        target="_self"
      >
        <div
          className="button-text"
        >
          I am a button
        </div>
      </a>
    </div>
  </div>
</div>
`;

exports[`Button component it renders the correct snapshot for an Icon Button 1`] = `
<div
  className="button  undefined "
>
  <div
    className="button-container"
    data-testid="test-simplified-button-button"
  >
    <div
      className="text-button "
    >
      <a
        className="button-base primary responsive-size medium active"
        href="#"
        id="test-simplified-button"
        onClick={[Function]}
        role="link"
        target="_self"
      >
        <div
          className="button-text"
        >
          simple text
        </div>
      </a>
    </div>
  </div>
</div>
`;
