// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Basic Modal component Matches DOM Snapshot 1`] = `
<div
  class="modal-dialog basic-modal  cox-resi modal-dialog-centered"
  data-testid="basic-modal"
  id="basic-modal"
>
  <div
    class="modal-content"
  >
    <div
      class="modal-header-container false modal-header"
      data-testid="basic-modal-header-container"
    >
      <div
        class="text-center modal-title h4"
        data-testid="basic-modal-header-title"
      >
        Modal Title
      </div>
      <button
        aria-label="Close"
        class="modal-close-btn"
        data-testid="basic-modal-close-btn"
        style="background: url(/content/dam/cox/common/icons/ui_components/close-river-blue.svg);"
      />
    </div>
    <div
      class="modal-body"
    >
      <div
        class="basic-modal-body-wrapper withoutImage"
        data-testid="basic-modal-withoutImage-modal-wrapper"
      >
        <div
          class="basic-modal-body-content withoutImage"
        >
          <div
            class="headline"
            data-testid="basic-modal-withoutImage-headline"
          >
            Get even more out of your high-speed home internet connection
          </div>
          <p
            class="subhead"
            data-testid="basic-modal-withoutImage-subhead"
          >
            Unlimited Data
          </p>
        </div>
      </div>
    </div>
    <div
      class="modal-footer"
    >
      <div
        class="button  undefined "
      >
        <div
          class="button-container"
          data-testid="basic-modal-secondary-button-button"
        >
          <div
            class="text-button "
          >
            <a
              class="button-base secondary responsive-size medium undefined"
              href="#"
              id="basic-modal-secondary-button"
              role="link"
              target="_self"
            >
              <div
                class="button-text"
              >
                Secondary Button
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
