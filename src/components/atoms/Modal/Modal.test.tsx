import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'

import Modal from './Modal'
import { ModalProps, ModalTypes } from './types'

describe('Basic Modal component', () => {
  const mockData = {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    handleClose: () => {},
    show: true,
    modalType: ModalTypes.withoutImage,
    title: 'Modal Title',
    showFooter: true,
    secondaryBtnText: 'Secondary Button',
    headline: 'Get even more out of your high-speed home internet connection',
    subhead: 'Unlimited Data',
    modalId: 'basic-modal'
  } as ModalProps

  test('renders Modal component', () => {
    const { container } = render(<Modal {...mockData} />)
    expect(container).toBeTruthy()
    expect(screen.getByTestId('basic-modal')).toBeTruthy()
  })
  it('Matches DOM Snapshot', () => {
    const { container } = render(<Modal {...mockData} />)
    expect(container).toBeTruthy()
    expect(screen.getByTestId('basic-modal')).toMatchSnapshot()
  })
})
