import { useEffect, useRef } from 'react'

import { RichTextProps } from './types'
import './RichText.scss'
import { useUdoContext } from '../../../contexts/UdoContext'
import { handleChatInfo, useOliverChat } from '../../../hooks/useOliverChat'
import { scrollToElementWithOffset, customParse } from '../../../utils/helper-util'
import { handleCarousel, handleModal } from '../../../utils/rte-util'
import { getTokenValues } from '../../../utils/rule-builder-util'
import { sanitizeRichTextHtml } from '../../../utils/xss-prevention-util'

const RichText = (props: RichTextProps): JSX.Element => {
  const { udo } = useUdoContext()
  const { isChatLoaded, options } = useOliverChat()
  const { id = 'rich-text', className = '', text = '', isParsed = false, tokenProperties = '' } = props
  const rawValue = isParsed ? text : getTokenValues(text, tokenProperties, udo?.localeName)
  // Sanitize the HTML content to prevent XSS attacks
  const sanitizedValue = typeof rawValue === 'string' ? sanitizeRichTextHtml(rawValue) : rawValue
  const value = sanitizedValue
  const editorRef = useRef<any>(null)

  useEffect(() => {
    const editor = editorRef.current
    if (editor) {
      const stickyNav = document?.querySelector('.react-sticky-container')
      if (stickyNav !== null) {
        const anchorLinks = editor.querySelectorAll('a[href^="#"]:not([data-link-type="carousel"])')
        anchorLinks?.forEach((element: any) => {
          element.addEventListener('click', scrollToElementWithOffset)
        })
      }

      editor?.querySelectorAll('a[data-link-type=modal]')?.forEach(function (anchor: HTMLAnchorElement) {
        anchor.addEventListener('click', handleModal)
      })
      editor?.querySelectorAll('a[data-link-type=carousel]')?.forEach(function (anchor: HTMLAnchorElement) {
        anchor.addEventListener('click', handleCarousel)
      })
    }
  }, [text])

  useEffect(() => {
    const editor = editorRef.current
    if (editor) {
      editor?.querySelectorAll('a[data-link-type=chat]')?.forEach(function (anchor: HTMLAnchorElement) {
        handleChatInfo(anchor, options, isChatLoaded, anchor.getAttribute('data-chat-question') ?? '')
      })
    }
  }, [isChatLoaded])

  return (
    <>
      {value && (
        <div
          data-rte-editelement={props['data-rte-editelement']}
          className={`word-wrap rte ${className} `}
          id={id}
          ref={editorRef}
          data-testid={`${id}-container`}
        >
          {value}
        </div>
      )}
    </>
  )
}

export default RichText
