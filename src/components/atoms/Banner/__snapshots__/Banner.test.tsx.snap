// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Banner component Matches DOM Snapshot of Authorable Logo Banner 1`] = `
<div
  className="banner-container "
  data-testid="banner-container"
  id="banner"
>
  <div
    className="authorable-banner-container"
    data-testid="-authorable-banner-container"
  >
    <div
      className="banner-logo"
    >
      <img
        alt="oliver"
        className="banner-icon-logo"
        src="/content/dam/cox/common/icons/ui_components/oliver.svg"
      />
      <div
        className="banner-content-logo"
      >
        <div
          className="banner-content-message"
        />
        <div
          className="banner-link"
        >
          <div
            className="button  undefined "
          >
            <div
              className="button-container"
              data-testid="-authorable-banner-button-button"
            >
              <div
                className="text-button text-center"
              >
                <a
                  className="button-base tertiary responsive-size medium active"
                  href="https://www.cox.com/residential/home.html"
                  id="-authorable-banner-button"
                  onClick={[Function]}
                  target="_blank"
                >
                  <div
                    className="button-text"
                  >
                    Learn more
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Banner component Matches DOM Snapshot of Authorable Primary Banner 1`] = `
<div
  className="banner-container "
  data-testid="banner-container"
  id="banner"
>
  <div
    className="authorable-banner-container"
    data-testid="-authorable-banner-container"
  >
    <div
      className="banner-primary"
    >
      <img
        alt="circle-question"
        className="banner-icon-primary"
        src="/content/dam/cox/common/icons/ui_components/circle-question.svg"
      />
      <div
        className="banner-content-primary"
      >
        <div
          className="banner-content-message"
        />
        <div
          className="banner-link"
        >
          <div
            className="button  undefined "
          >
            <div
              className="button-container"
              data-testid="-authorable-banner-button-button"
            >
              <div
                className="text-button text-center"
              >
                <a
                  className="button-base tertiary responsive-size medium active"
                  href="https://www.cox.com/residential/home.html"
                  id="-authorable-banner-button"
                  onClick={[Function]}
                  target="_blank"
                >
                  <div
                    className="button-text"
                  >
                    Learn more
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Banner component Matches DOM Snapshot of Dynamic ERROR Banner 1`] = `
<div
  className="banner-container "
  data-testid="banner-container"
  id="banner"
>
  <div
    className="dynamic-banner"
    data-testid="-dynamic-banner"
  >
    <div
      className="banner-container error "
    >
      <img
        alt="circle-exclamation-pure-orange"
        className="all-variations-banner-icon error-banner-icon"
        src="/content/dam/cox/common/icons/ui_components/circle-exclamation-pure-orange.svg"
      />
      <div
        className="banner-content-container error-content-container"
      >
        <div
          className="banner-content"
        />
        <div
          className="banner-link"
        >
          <div
            className="button  undefined "
          >
            <div
              className="button-container"
              data-testid="-dynamic-banner-button-button"
            >
              <div
                className="text-button text-center"
              >
                <a
                  className="button-base tertiary responsive-size medium active"
                  href="https://www.cox.com/residential/home.html"
                  id="-dynamic-banner-button"
                  onClick={[Function]}
                  target="_blank"
                >
                  <div
                    className="button-text"
                  >
                    Learn more
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Banner component Matches DOM Snapshot of Dynamic LOGO Banner 1`] = `
<div
  className="banner-container "
  data-testid="banner-container"
  id="banner"
>
  <div
    className="dynamic-banner"
    data-testid="-dynamic-banner"
  >
    <div
      className="banner-container logo "
    >
      <img
        alt="circle-exclamation-pure-orange"
        className="all-variations-banner-icon logo-banner-icon"
        src="/content/dam/cox/common/icons/ui_components/circle-exclamation-pure-orange.svg"
      />
      <div
        className="banner-content-container logo-content-container"
      >
        <div
          className="banner-content"
        />
        <div
          className="banner-link"
        >
          <div
            className="button  undefined "
          >
            <div
              className="button-container"
              data-testid="-dynamic-banner-button-button"
            >
              <div
                className="text-button text-center"
              >
                <a
                  className="button-base tertiary responsive-size medium active"
                  href="https://www.cox.com/residential/home.html"
                  id="-dynamic-banner-button"
                  onClick={[Function]}
                  target="_blank"
                >
                  <div
                    className="button-text"
                  >
                    Learn more
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Banner component Matches DOM Snapshot of Dynamic PRIMARY Banner 1`] = `
<div
  className="banner-container "
  data-testid="banner-container"
  id="banner"
>
  <div
    className="dynamic-banner"
    data-testid="-dynamic-banner"
  >
    <div
      className="banner-container primary "
    >
      <img
        alt="circle-exclamation-pure-orange"
        className="all-variations-banner-icon primary-banner-icon"
        src="/content/dam/cox/common/icons/ui_components/circle-exclamation-pure-orange.svg"
      />
      <div
        className="banner-content-container primary-content-container"
      >
        <div
          className="banner-content"
        />
        <div
          className="banner-link"
        >
          <div
            className="button  undefined "
          >
            <div
              className="button-container"
              data-testid="-dynamic-banner-button-button"
            >
              <div
                className="text-button text-center"
              >
                <a
                  className="button-base tertiary responsive-size medium active"
                  href="https://www.cox.com/residential/home.html"
                  id="-dynamic-banner-button"
                  onClick={[Function]}
                  target="_blank"
                >
                  <div
                    className="button-text"
                  >
                    Learn more
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Banner component Matches DOM Snapshot of Dynamic SUCCESS Banner 1`] = `
<div
  className="banner-container "
  data-testid="banner-container"
  id="banner"
>
  <div
    className="dynamic-banner"
    data-testid="-dynamic-banner"
  >
    <div
      className="banner-container success "
    >
      <img
        alt="circle-exclamation-pure-orange"
        className="all-variations-banner-icon success-banner-icon"
        src="/content/dam/cox/common/icons/ui_components/circle-exclamation-pure-orange.svg"
      />
      <div
        className="banner-content-container success-content-container"
      >
        <div
          className="banner-content"
        />
        <div
          className="banner-link"
        >
          <div
            className="button  undefined "
          >
            <div
              className="button-container"
              data-testid="-dynamic-banner-button-button"
            >
              <div
                className="text-button text-center"
              >
                <a
                  className="button-base tertiary responsive-size medium active"
                  href="https://www.cox.com/residential/home.html"
                  id="-dynamic-banner-button"
                  onClick={[Function]}
                  target="_blank"
                >
                  <div
                    className="button-text"
                  >
                    Learn more
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Banner component Matches DOM Snapshot of Dynamic WARNING Banner 1`] = `
<div
  className="banner-container "
  data-testid="banner-container"
  id="banner"
>
  <div
    className="dynamic-banner"
    data-testid="-dynamic-banner"
  >
    <div
      className="banner-container warning "
    >
      <img
        alt="circle-exclamation-pure-orange"
        className="all-variations-banner-icon warning-banner-icon"
        src="/content/dam/cox/common/icons/ui_components/circle-exclamation-pure-orange.svg"
      />
      <div
        className="banner-content-container warning-content-container"
      >
        <div
          className="banner-content"
        />
        <div
          className="banner-link"
        >
          <div
            className="button  undefined "
          >
            <div
              className="button-container"
              data-testid="-dynamic-banner-button-button"
            >
              <div
                className="text-button text-center"
              >
                <a
                  className="button-base tertiary responsive-size medium active"
                  href="https://www.cox.com/residential/home.html"
                  id="-dynamic-banner-button"
                  onClick={[Function]}
                  target="_blank"
                >
                  <div
                    className="button-text"
                  >
                    Learn more
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Banner component Matches DOM Snapshot of Promo Banner 1`] = `
<div
  className="banner-container "
  data-testid="banner-container"
  id="banner"
>
  <div
    className="promo-banner-container"
    data-testid="-promo-banner-container"
  >
    <div
      className="promo-border-wrap"
    >
      <img
        alt="oliver"
        className="banner-promo-icon"
        src="/content/dam/cox/common/icons/ui_components/oliver.svg"
      />
      <div
        className="banner-promo-content-wrapper"
      >
        <div
          className="banner-promo-message"
        />
        <div
          className="button  undefined "
        >
          <div
            className="button-container"
            data-testid="-promo-banner-button-button"
          >
            <div
              className="text-button text-center"
            >
              <a
                className="button-base primary responsive-size medium active"
                href="https://www.cox.com/residential/home.html"
                id="-promo-banner-button"
                onClick={[Function]}
                role="link"
                target="_blank"
              >
                <div
                  className="button-text"
                >
                  Learn more
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
