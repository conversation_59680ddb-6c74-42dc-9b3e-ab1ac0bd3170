import { useEffect } from 'react'

import { ADOBE_TARGET_VISITOR_INSTANCE, HASH, HREF, OPTIMIZATION_URL, UTAG_URL } from '../../../../constants'
import { useAppDataContext } from '../../../../contexts/AppDataContext'
import { LobExperience } from '../../../../types'
import { loadScript } from '../../../../utils/utag-util'

const ScriptRenderer = () => {
  const { appData } = useAppDataContext()
  const displayBBFL = appData?.page?.displayBBFL
  const bbflPath = appData?.page?.bbflPath

  const loadOptimization = () => {
    loadScript({ url: OPTIMIZATION_URL, id: 'optimization', isAsync: false })
  }
  const handleTarget = () => {
    setTimeout(() => {
      if (!HREF.includes(LobExperience.BUSINESS) && window?.adobe?.target) {
        console.info('Target is available')
        triggerTargetEvent()
        document.addEventListener(window?.adobe?.target?.event?.CONTENT_RENDERING_START, function () {
          const visitor = window?.Visitor.getInstance(ADOBE_TARGET_VISITOR_INSTANCE)
          visitor.resetState()
        })
      } else {
        handleUtag()
        if (!HREF.includes(LobExperience.BUSINESS)) {
          console.error('Target not available')
        }
      }
    }, 1000)
  }

  const observeDom = () => {
    // Create a new MutationObserver instance
    const observer = new MutationObserver((mutationsList, observer) => {
      // Look through all mutations that just occured
      for (const mutation of mutationsList) {
        // If the addedNodes property has one or more nodes
        if (mutation.addedNodes.length) {
          const hash = window ? HASH : ''
          if (hash === '') {
            window && window.scrollTo(0, 0)
            observer.disconnect() // Stop observing
          } else {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-ignore
            const targetElement = mutation.target.querySelector(hash)
            const stickyNav = document.querySelector('.react-sticky-container.is-sticky')
            if (targetElement?.getBoundingClientRect()?.height) {
              setTimeout(() => {
                const y =
                  targetElement?.getBoundingClientRect()?.top +
                  window.scrollY -
                  (stickyNav?.getBoundingClientRect()?.height ?? 120)
                window.scrollTo({ top: y, behavior: 'smooth' })
              }, 1000)
              observer.disconnect() // Stop observing
            }
          }
        }
      }
    })

    // Start observing the document with the configured parameters
    observer.observe(document, { childList: true, subtree: true })
  }

  const loadBBFL = () => {
    if (displayBBFL === 'true') {
      loadScript({ url: bbflPath, id: 'cox-bbfl' })
    }
  }

  const triggerTargetEvent = () => {
    window?.adobe?.target?.triggerView('REACT PAGE LOADED', { page: true })
    console.info('Triggered REACT PAGE LOADED view')
    handleUtag()
  }

  const handleUtag = () => {
    setTimeout(() => {
      loadScript({ url: UTAG_URL, id: 'utag-js', isAsync: true })
    }, 4000)
  }

  useEffect(() => {
    observeDom()
    loadOptimization()
    handleTarget()
    loadBBFL()
  }, [])

  return <></>
}

export default ScriptRenderer
