// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SolutionList component Matches DOM Snapshot for Busi 1`] = `
<div
  className="solutions-list container"
  data-testid="solutionlist-wrapper"
>
  <div
    className="solutionlist-business-container"
    data-testid="solutionlist-business-container"
  >
    <div
      className=" section-header-container  "
      data-testid="section-header"
    >
      <div
        className=" main-heading"
        data-testid="section-header-title"
      >
        <div
          className="word-wrap rte  "
          data-testid="rich-text-container"
          id="rich-text"
        >
          This is title
        </div>
         
      </div>
    </div>
    <div
      className="solutions-container-one"
      data-testid="solutionlist-items"
    >
      <div
        className="col-12 solutions"
      >
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              About Cox Business Fax to Email
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              Cox Business Fax
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              Business Fax to Email
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              Fax to Email
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              Cox Business Fax to Email
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              Lean about the Cox 
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              About Cox Business Fax to Email 31
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              About Cox Business Fax to Email 32
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              About Cox Business Fax to Email 33
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              About Cox Business Fax to Email 41
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              About Cox Business Fax to Email 42
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
      </div>
      <div
        className="col-12 solutions"
      >
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              About Cox Business Fax to Email 43
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              About Cox Business Fax to Email 5
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              About Cox Business Fax to Email 52
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              Page5 Item53
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              About Cox Business Fax to Email 61
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              About Cox Business Fax to Email 62
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              requirements, and supported operating systems
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              About Cox Business Fax to Email 71
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              About Cox Business Fax to Email 72
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
        <div
          className="solution"
          data-testid="solutionlist-item"
        >
          <div
            className="link__container"
            data-testid="link-container"
          >
            <a
              className="link__anchor"
              target="_blank"
            >
              About Cox Business Fax to Email 73
              <img
                alt=""
                className="link__icon"
                data-testid="link-icon"
                src="/content/dam/cox/common/icons/ui_components/chevron-right-river-blue.svg"
              />
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SolutionList component Matches DOM Snapshot for Resi 1`] = `
<div
  className="solutions-list container"
  data-testid="solutionlist-wrapper"
>
  <div
    className="solutionlist-container"
    data-testid="solutionlist-container"
  >
    <div
      className="solutionlist-details"
    >
      <div
        className="word-wrap rte title "
        data-testid="rich-text-container"
        id="rich-text"
      >
        This is title
      </div>
      <div
        className="word-wrap rte description "
        data-testid="rich-text-container"
        id="rich-text"
      >
        This is Description
      </div>
    </div>
    <div
      className="solutions-container"
      data-testid="solutionlist-items"
    >
      <div
        className="solution"
        data-testid="solutionlist-item"
      >
        <div
          className="title"
        >
          <a
            target="_blank"
          >
            About Cox Business Fax to Email
          </a>
        </div>
        <div
          className="description"
        >
          <span>
            Lean about the Cox Business Fax to Email service that allows you to receive faxes via email. Lean about the Cox Business Fax to Email service that all... 
          </span>
          <span
            className="read-more"
            onClick={[Function]}
          >
            read more
          </span>
        </div>
      </div>
      <div
        className="solution"
        data-testid="solutionlist-item"
      >
        <div
          className="title"
        >
          <a
            target="_blank"
          >
            Cox Business Fax
          </a>
        </div>
        <div
          className="cox-typography cox-text-paragraph3-regular description"
          data-testid="cox-typography"
          id=""
        >
          Cox Business Fax to Email service that allows you to receive faxes via email
        </div>
      </div>
      <div
        className="solution"
        data-testid="solutionlist-item"
      >
        <div
          className="title"
        >
          <a
            target="_blank"
          >
            Business Fax to Email
          </a>
        </div>
        <div
          className="description"
        >
          <span>
            Email service that allows you to receive faxes via email, Lean about the Cox Business Fax to Email service that allows you to receive faxes via email.... 
          </span>
          <span
            className="read-more"
            onClick={[Function]}
          >
            read more
          </span>
        </div>
      </div>
    </div>
  </div>
  <div
    className="pagination-container"
    data-testid="pagination-container"
  >
    <ul>
      <li
        className="pagination-page first disable-custom"
        onClick={[Function]}
      >
        <img
          alt="pagination first"
          src="/content/dam/cox/common/icons/ui_components/pagination-first-disabled.svg"
        />
      </li>
      <li
        className="pagination-page previous disable-custom"
        onClick={[Function]}
      >
        <img
          alt="pagination previous"
          src="/content/dam/cox/common/icons/ui_components/pagination-prev-disabled.svg"
        />
      </li>
      <li>
        <ul
          className="pagination-numbers"
        >
          <li
            className="pagination-number active"
            data-testid="pagination-list-item-1"
            onClick={[Function]}
          >
            1
          </li>
          <li
            className="pagination-number "
            data-testid="pagination-list-item-2"
            onClick={[Function]}
          >
            2
          </li>
          <li
            className="pagination-number "
            data-testid="pagination-list-item-3"
            onClick={[Function]}
          >
            3
          </li>
          <li
            className="pagination-number "
            data-testid="pagination-list-item-4"
            onClick={[Function]}
          >
            4
          </li>
          <li
            className="pagination-number "
            data-testid="pagination-list-item-5"
            onClick={[Function]}
          >
            5
          </li>
          <li
            className="pagination-number "
            data-testid="pagination-list-item-6"
            onClick={[Function]}
          >
            6
          </li>
          <li
            className="pagination-number "
            data-testid="pagination-list-item-7"
            onClick={[Function]}
          >
            7
          </li>
        </ul>
      </li>
      <li
        className="pagination-page next "
        onClick={[Function]}
      >
        <img
          alt="pagination next"
          src="/content/dam/cox/common/icons/ui_components/pagination-next-active.svg"
        />
      </li>
      <li
        className="pagination-page last "
        onClick={[Function]}
      >
        <img
          alt="pagination last"
          src="/content/dam/cox/common/icons/ui_components/pagination-last-active.svg"
        />
      </li>
    </ul>
  </div>
</div>
`;
