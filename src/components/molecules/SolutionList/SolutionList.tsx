import { useState } from 'react'

import { Solution, SolutionListProps } from './types'
import './SolutionList.scss'
import { CHEVRON_RIGHT_BLUE } from '../../../assets/images'
import useLobExperience from '../../../hooks/useLobExperience'
import { CoxThemeClass } from '../../../types'
import Link from '../../atoms/Link'
import Pagination from '../../atoms/Pagination'
import RichText from '../../atoms/RichText'
import SectionHeader from '../../atoms/SectionHeader'
import Typography, { TypographyTokens } from '../../atoms/Typography'

const SolutionList = (props: SolutionListProps) => {
  const {
    componentTitle = '',
    componentDescription = '',
    solutionsList,
    pagesList,
    className,
    pagination,
    lob,
    articleicon = CHEVRON_RIGHT_BLUE,
    twocolumn = false
  } = props
  const initialPaginatedList = solutionsList.filter((solution) => solution.pageNumber === 'page-1')
  const [paginatedSolutionList, setPaginatedSolutionList] = useState(initialPaginatedList)
  const [expandedIndexes, setExpandedIndexes] = useState<number[]>([])
  const pageNumbers: number[] = pagesList.map((page) => parseInt(page))
  const hasSolutions = solutionsList.length > 0 && paginatedSolutionList?.length > 0
  const { isBusiness } = useLobExperience()

  const selectPage = (pageNumber: number) => {
    const paginatedList = solutionsList.filter((solution) => solution.pageNumber === `page-${pageNumber}`)
    setPaginatedSolutionList(paginatedList)
    setExpandedIndexes([])
  }

  const setIndexes = (index: number) => {
    if (!expandedIndexes.includes(index)) {
      setExpandedIndexes([...expandedIndexes, index])
    }
  }

  const getDescription = (index: number, description: string) => {
    const charLimit = 150
    if (description) {
      if (description.length <= charLimit || expandedIndexes.includes(index)) {
        return <Typography text={description} additionalClass='description' style={TypographyTokens.P3_REGULAR} />
      } else {
        const ellipsis = '... '
        const shortDesc = description.substring(0, charLimit) + ellipsis
        return (
          <div className='description'>
            <span>{shortDesc}</span>
            <span className='read-more' onClick={() => setIndexes(index)}>
              read more
            </span>
          </div>
        )
      }
    }
    return ''
  }

  const splitBusiSolutions = (solutionsList: Solution[]) => {
    const half = Math.ceil(solutionsList.length / 2)
    return [solutionsList.slice(0, half), solutionsList.slice(half)]
  }

  const [firstHalf, secondHalf] = splitBusiSolutions(solutionsList)

  if (isBusiness || lob === CoxThemeClass.BUSINESS) {
    return (
      <div className={`solutions-list ${className}`} data-testid={`solutionlist-wrapper`}>
        <div className='solutionlist-business-container' data-testid={`solutionlist-business-container`}>
          <SectionHeader
            title={{
              value: componentTitle,
              isRTE: true
            }}
          />
          <div className={`solutions-container-${twocolumn ? 'two' : 'one'}`} data-testid={`solutionlist-items`}>
            {[firstHalf, secondHalf].map((list, idx) => (
              <div className={`${twocolumn ? 'col-12 col-lg-6' : 'col-12'} solutions`} key={`list-${idx}`}>
                {list.map(({ path, title = '' }) => (
                  <div className='solution' key={title} data-testid='solutionlist-item'>
                    <Link linkText={title} href={path} linkTextIconValue={articleicon} target={'_blank'} />
                  </div>
                ))}
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`solutions-list ${className}`} data-testid={`solutionlist-wrapper`}>
      <div className='solutionlist-container' data-testid={`solutionlist-container`}>
        <div className='solutionlist-details'>
          <RichText text={componentTitle} className='title' />
          <RichText text={componentDescription} className='description' />
        </div>
        <div className='solutions-container' data-testid={`solutionlist-items`}>
          {hasSolutions &&
            paginatedSolutionList.map((solution: Solution, index: number) => {
              const { path, title = '', description = '' } = solution
              return (
                <div className='solution' key={title} data-testid={`solutionlist-item`}>
                  <div className='title'>
                    <a href={path} target='_blank'>
                      {title}
                    </a>
                  </div>
                  {getDescription(index, description)}
                </div>
              )
            })}
        </div>
      </div>
      {pagination && <Pagination pageNumbers={pageNumbers} pageClick={(pageNumber: number) => selectPage(pageNumber)} />}
    </div>
  )
}

export default SolutionList
