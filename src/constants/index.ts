import { LobExperience } from '../types'
import { replaceColon } from '../utils/helper-util'

export const {
  NODE_ENV = '',
  REACT_APP_AEM_AUTHORIZATION_HEADER = '',
  REACT_APP_ROOT = '',
  REACT_APP_API_HOST
} = process.env

export const AEM_CONSTANTS = {
  TYPE_PROP: ':type',
  ITEMS_PROP: ':items',
  ITEMS_ORDER_PROP: ':itemsOrder',
  COMPONENTS_PATH: 'cox-cms-react/components'
}

export const CSRF_TOKEN = window?.RequestJson?.page?.reserved?.csrfToken
export const PATHNAME = window?.location?.pathname
export const HOST = window?.location?.host
export const ORIGIN = window?.location?.origin
export const HREF = window?.location?.href
export const HASH = window?.location?.hash
export const SEARCH = window?.location?.search

export const AUTHOR_DOMAINS = {
  DEV: `https://author-p47652-e526536.adobeaemcloud.com`,
  QA1: `https://author-p47652-e412725.adobeaemcloud.com`,
  QA2: `https://author-p47652-e412678.adobeaemcloud.com`,
  QALOAD: `https://author-p47652-e543711.adobeaemcloud.com`,
  STAGE: `https://author-p47652-e412724.adobeaemcloud.com`,
  PROD: `https://author-p47652-e412677.adobeaemcloud.com`
}

export const PUBLISHER_DOMAINS = {
  DEV: `https://publish-p47652-e526536.adobeaemcloud.com`,
  QA1: `https://publish-p47652-e412725.adobeaemcloud.com`,
  QA2: `https://publish-p47652-e412678.adobeaemcloud.com`,
  QALOAD: `https://publish-p47652-e543711.adobeaemcloud.com`,
  STAGE: `https://publish-p47652-e412724.adobeaemcloud.com`,
  PROD: `https://publish-p47652-e412677.adobeaemcloud.com`
}

export const COX_DOMAINS = {
  DEV: 'https://www.int.dev.cox.com',
  QA1: 'https://www.one.qa.cox.com',
  QA2: 'https://www.two.qa.cox.com',
  QALOAD: 'https://www.one.staging.cox.com',
  STAGE: 'https://test.cox.com',
  PROD: 'https://www.cox.com'
}

export const SPANISH_COX_DOMAINS = {
  DEV: 'https://www.int.dev.cox.com',
  QA1: 'https://www.one.qa.cox.com',
  QA2: 'https://www.two.qa.cox.com',
  QALOAD: 'https://www.one.staging.cox.com',
  STAGE: 'https://espanol.test.cox.com',
  PROD: 'https://espanol.cox.com'
}

export const COX_DOMAIN_REGEX = {
  DEV: /.*int\.dev\.cox\.com/,
  QA1: /.*one\.qa\.cox\.com/,
  QA2: /.*two\.qa\.cox\.com/,
  QALOAD: /.*staging\.cox\.com/,
  STAGE: /.*test\.cox\.com/,
  PROD: /.*.cox\.com/
}

export const isLocalHost = () => window.location.href.includes('localhost')
export const isDev = [AUTHOR_DOMAINS.DEV, PUBLISHER_DOMAINS.DEV, COX_DOMAINS.DEV, SPANISH_COX_DOMAINS.DEV]
export const isQA1 = [AUTHOR_DOMAINS.QA1, PUBLISHER_DOMAINS.QA1, COX_DOMAINS.QA1, SPANISH_COX_DOMAINS.QA1]
export const isQA2 = [AUTHOR_DOMAINS.QA2, PUBLISHER_DOMAINS.QA2, COX_DOMAINS.QA2, SPANISH_COX_DOMAINS.QA2]
export const isQALoad = [AUTHOR_DOMAINS.QALOAD, PUBLISHER_DOMAINS.QALOAD, COX_DOMAINS.QALOAD, SPANISH_COX_DOMAINS.QALOAD]
export const isStage = [AUTHOR_DOMAINS.STAGE, PUBLISHER_DOMAINS.STAGE, COX_DOMAINS.STAGE, SPANISH_COX_DOMAINS.STAGE]
export const isProd = [AUTHOR_DOMAINS.PROD, PUBLISHER_DOMAINS.PROD, COX_DOMAINS.PROD, SPANISH_COX_DOMAINS.PROD]

/**UTAG  START*/
export const RESI_UTAG_URLS = {
  DEV: 'https://tags.tiqcdn.com/utag/cox/main/dev/utag.js',
  QA1: 'https://tags.tiqcdn.com/utag/cox/main/qa/utag.js',
  QA2: 'https://tags.tiqcdn.com/utag/cox/main/qa/utag.js',
  QALOAD: 'https://tags.tiqcdn.com/utag/cox/main/qa/utag.js',
  STAGE: 'https://tags.tiqcdn.com/utag/cox/main/prod/utag.js',
  PROD: 'https://tags.tiqcdn.com/utag/cox/main/prod/utag.js'
}

export const BUSI_UTAG_URLS = {
  DEV: 'https://tags.tiqcdn.com/utag/cox/cbprimary/dev/utag.js',
  QA1: 'https://tags.tiqcdn.com/utag/cox/cbprimary/qa/utag.js',
  QA2: 'https://tags.tiqcdn.com/utag/cox/cbprimary/qa/utag.js',
  QALOAD: 'https://tags.tiqcdn.com/utag/cox/cbprimary/qa/utag.js',
  STAGE: 'https://tags.tiqcdn.com/utag/cox/cbprimary/prod/utag.js',
  PROD: 'https://tags.tiqcdn.com/utag/cox/cbprimary/prod/utag.js'
}

export const COX_BUSI_URLS = {
  DEV: 'https://myaccount.dev1.coxbusiness.com',
  QA1: 'https://myaccount.qa1.coxbusiness.com',
  QA2: 'https://myaccount.qa2.coxbusiness.com',
  QALOAD: 'https://myaccount.st1.coxbusiness.com',
  STAGE: 'https://myaccount-business.ppe.cox.com',
  PROD: 'https://myaccount.coxbusiness.com',
  DEFAULT: 'https://myaccount-business.cox.com'
}

const getUtagUrl = (origin: string) => {
  if (HREF.includes(LobExperience.BUSINESS)) {
    if (COX_DOMAIN_REGEX.DEV.test(origin)) return BUSI_UTAG_URLS.DEV
    else if (COX_DOMAIN_REGEX.QA1.test(origin)) return BUSI_UTAG_URLS.QA1
    else if (COX_DOMAIN_REGEX.QA2.test(origin)) return BUSI_UTAG_URLS.QA2
    else if (COX_DOMAIN_REGEX.QALOAD.test(origin)) return BUSI_UTAG_URLS.QALOAD
    else if (COX_DOMAIN_REGEX.STAGE.test(origin)) return BUSI_UTAG_URLS.STAGE
    else if (COX_DOMAIN_REGEX.PROD.test(origin)) return BUSI_UTAG_URLS.PROD
    else return BUSI_UTAG_URLS.STAGE
  } else {
    if (COX_DOMAIN_REGEX.DEV.test(origin)) return RESI_UTAG_URLS.DEV
    else if (COX_DOMAIN_REGEX.QA1.test(origin)) return RESI_UTAG_URLS.QA1
    else if (COX_DOMAIN_REGEX.QA2.test(origin)) return RESI_UTAG_URLS.QA2
    else if (COX_DOMAIN_REGEX.QALOAD.test(origin)) return RESI_UTAG_URLS.QALOAD
    else if (COX_DOMAIN_REGEX.STAGE.test(origin)) return RESI_UTAG_URLS.STAGE
    else if (COX_DOMAIN_REGEX.PROD.test(origin)) return RESI_UTAG_URLS.PROD
    else return RESI_UTAG_URLS.STAGE
  }
}

export const UTAG_URL = getUtagUrl(ORIGIN)
/**UTAG END */

/**MOTION POINT  START*/
export const MOTION_POINT_SNIPPET_URLS = {
  STAGE: '/content/dam/cox/apps/common/scripts/qa/mp_snippet.js',
  PROD: '/content/dam/cox/apps/common/scripts/prod/mp_snippet.js'
}

export const getMPSnippetUrl = (origin: string) => {
  if (isProd.includes(origin)) return MOTION_POINT_SNIPPET_URLS.PROD
  else if (isStage.includes(origin)) return MOTION_POINT_SNIPPET_URLS.STAGE
  return ''
}

export const MOTION_POINT_SNIPPET_URL = getMPSnippetUrl(ORIGIN)
export const MOTION_POINT_EASY_LINK = 'https://coxcommunications.mpeasylink.com/mpel/mpel.js'
/**MOTION POINT  END*/

/**LOG OUT START*/
export const LOGOUT_URL = '/webapi/cdncache/cookieset?resource='
export const SIMPLE_LOG_OFF = '/simpleLogoff?onsuccess='
export const API_KEY_BUSI_PROFILE_MENU = '4c25b2f0-e698-4982-84fa-56c7c535e4a5'
export const CLIENT_ID_BUSI_PROFILE_MENU = 'aemuser'

export const getLogoutUrl = (origin: string, linkUrl: string, lob: boolean) => {
  const generateUrl = (url: string) => {
    const busiURL = replaceColon(`${linkUrl + ORIGIN + url + HREF}`)
    const otherURL = replaceColon(`${ORIGIN + linkUrl + ORIGIN + url + HREF}`)
    return !lob ? otherURL : busiURL
  }

  const appContext = PATHNAME.split('/')[1]

  if (COX_DOMAIN_REGEX.QALOAD.test(origin)) return generateUrl('/' + appContext + SIMPLE_LOG_OFF)
  else return generateUrl(LOGOUT_URL)
}
/**LOG OUT END*/

/**COX DOMAINS START */
const getCoxDomainUrl = (origin: string) => {
  if (isDev.includes(origin)) return COX_DOMAINS.DEV
  else if (isQA1.includes(origin)) return COX_DOMAINS.QA1
  else if (isQA2.includes(origin)) return COX_DOMAINS.QA2
  else if (isQALoad.includes(origin)) return COX_DOMAINS.QALOAD
  else if (isStage.includes(origin)) return COX_DOMAINS.STAGE
  else if (isProd.includes(origin)) return COX_DOMAINS.PROD
  else return COX_DOMAINS.STAGE
}

const getCoxBusiDomainUrlPrefix = (origin: string) => {
  if (isDev.includes(origin)) return COX_BUSI_URLS.DEV
  else if (isQA1.includes(origin)) return COX_BUSI_URLS.QA1
  else if (isQA2.includes(origin)) return COX_BUSI_URLS.QA2
  else if (isQALoad.includes(origin)) return COX_BUSI_URLS.QALOAD
  else if (isStage.includes(origin)) return COX_BUSI_URLS.STAGE
  else if (isProd.includes(origin)) return COX_BUSI_URLS.PROD
  else return COX_BUSI_URLS.STAGE
}

const getCoxBusiDomainUrlSuffix = (origin: string) => {
  if (isStage.includes(origin)) return COX_BUSI_URLS.STAGE
  else return COX_BUSI_URLS.DEFAULT
}

export const COX_DOMAIN_URL = getCoxDomainUrl(ORIGIN)
export const SIGN_IN_MENU_URL = 'https://myaccount-business.cox.com/api/cbma/external/loginprofiles/menu'

/** WEB APIS */
export const WEB_APIS = {
  CTAM_API: `${COX_DOMAIN_URL}/webapi/aem/ctam-token`,
  FLEX_OFFERS_API: `${COX_DOMAIN_URL}/webapi/aem/channelsales/getflexoffers`,
  FLEX_OFFER_ADDRESS_API: `${COX_DOMAIN_URL}/webapi/aem/channelsales/getflexoffers/address`,
  ADDRESS_SERVICEABILITY_API: `${COX_DOMAIN_URL}/webapi-wafr3/aem/addressserviceability`,
  HEADER_PROFILE_RESIDENTIAL_API: `${COX_DOMAIN_URL}/webapi/aem/headerprofile?webp=true`,
  HEADER_PROFILE_BUSINESS_API: `${COX_DOMAIN_URL}/webcbapi/aem/headerprofile?webp=true`,
  BELL_NOTIFICATION_API: `${COX_DOMAIN_URL}/webapi-wafr3/aem/user-alerts?&contextID=WEB_SWAP_CONTEXT2&count=5`,
  SET_DISPOSITION_API: `${COX_DOMAIN_URL}/webapi/aem/setdisposition`,
  YOUTUBE_API: `${COX_DOMAIN_URL}/webapi/aem/youtube-service`,
  CURRENT_SUBSCRIPTION: `${COX_DOMAIN_URL}/webapi/aem/currentsubscription`,
  SET_DISPOSITION: `${COX_DOMAIN_URL}/webapi/aem/setdisposition`
}

/** AEM PATHS */
export const DAM_ICON_PATH = '/content/dam/cox/common/icons/ui_components/'

/**Geo Location */
export const SERVICABLE_CITIES_API = 'https://framework.cox.com/presentation/rest/3.0/locations/cities/'
export const COX_BUSINESS_HOME_URL = 'https://www.cox.com/business/home.html'
export const COX_RESI_HOME_URL = 'https://www.cox.com/residential/home.html'
export const FIND_NEW_PROVIDER_URL = 'https://www.smartmove.us/find?ccode=MRCX1010&zip='
export const COX_RESIDENTIAL_SIGN_IN_URL =
  'https://www.cox.com/content/dam/cox/okta/signin.html?onsuccess=https%3A%2F%2Fwww.cox.com%2Fwebapi%2Fcdncache%2Fcookieset%3Fresource%3Dhttps%3A%2F%2Fwww.cox.com%2Fresaccount%2Fhome.html'
export const COX_BUSI_SIGN_IN_URL = `${getCoxBusiDomainUrlPrefix(ORIGIN)}/cbma/unauth/login?onsuccess=${encodeURIComponent(
  getCoxDomainUrl(ORIGIN)
)}%2Fwebapi%2Fcdncache%2Fcookieset%3Fresource%3D${encodeURIComponent(getCoxBusiDomainUrlSuffix(ORIGIN))}`
/** Dynamic Media Paths */
export const DYNAMIC_MEDIA_STAGING = 'https://assets.cox.com/is/image/coxstage/'
export const DYNAMIC_MEDIA_PROD = 'https://assets.cox.com/is/image/cox/'

/**Address Capture */
export const SMART_MOVE_SEARCH_API = 'https://www.smartmove.us/Widget/SmartMove/smartmovesearch.js'
export const GOOGLE_MAP_KEY =
  'https://maps.googleapis.com/maps/api/js?client=gme-coxenterprises&v=3.exp&channel=cci-cox-centers&libraries=places'

/**Yext Search */
export const YEXT_URL = 'https://liveapi-cached.yext.com/v2/accounts/me/answers/'

/**List items on skeleton */
export const SKELETON_ITEMS = ['1', '2', '3']

/* zindex const in tsx files */
export const HEADER_OVERLAY_ZINDEX = 999
export const FOOTER_OVERLAY_ZINDEX = 1019
export const STICKY_OVERLAY_ZINDEX = 1040

/**Target */
export const ADOBE_TARGET_VISITOR_INSTANCE = '8C6767C25245AD1A0A490D4C@AdobeOrg'

export const OPTIMIZATION_URL = isProd.includes(ORIGIN)
  ? 'https://webcdn.cox.com/content/dam/cox/apps/common/scripts/prod/optimization.js'
  : 'https://webcdn.cox.com/content/dam/cox/apps/common/scripts/qa/optimization.js'

/**JSON Paths Start */
export const LAYOUT_XF_PATH = `/content/experience-fragments/cox/residential/ui8globalnav/`
export const HEADER_XF = `${COX_DOMAIN_URL}${LAYOUT_XF_PATH}global-header/master.model.json`
export const FOOTER_XF = `${COX_DOMAIN_URL}${LAYOUT_XF_PATH}global-footer/master.model.json`
export const TOP_LINKS_PATH = `${COX_DOMAIN_URL}/content/dam/cox/common/ui8/header-toplinks.json`
export const GEO_LOCATION_STATES_PATH = `${COX_DOMAIN_URL}/content/dam/cox/common/ui8/geo-location-states.json`
export const SIDE_NAVIGATION_DETAILS_PATH = `${COX_DOMAIN_URL}/content/dam/cox/common/ui8/side-navigation-details.json`
export const FOOTER_LINKS_PATH = `${COX_DOMAIN_URL}/content/dam/cox/common/ui8/footer-bottomlinks.json`
/**JSON Paths End */
