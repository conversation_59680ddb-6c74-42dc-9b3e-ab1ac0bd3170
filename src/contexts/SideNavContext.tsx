import { createContext, useContext, useMemo, useState } from 'react'
export const SideNavContext = createContext({
  sideNavContext: [],
  setSideNavContext: (navigationDetails: any) => {
    console.info(navigationDetails)
  },
  selectedSideNav: {}
})
SideNavContext.displayName = 'SideNavContext'

export const SideNavProvider = ({ children }: any) => {
  const [sideNavContext, setSideNavContext] = useState<any>([])
  const setter = (newValue: any) => {
    setSideNavContext(() => [...newValue])
  }
  const getSelectedSideNav = () => {
    return sideNavContext
      .map((item: any) => {
        if (item.subNavItems) {
          const selectedSubNavItem = item.subNavItems.find((subItem: any) => subItem.selected === true)
          if (selectedSubNavItem) {
            return selectedSubNavItem // Return the selected subNavItem
          }
        }
        return item.selected === true ? item : null
      })
      .filter((item: any) => item !== null)[0]
  }
  const valueObject = useMemo(() => {
    return {
      sideNavContext,
      setSideNavContext: setter,
      selectedSideNav: getSelectedSideNav()
    }
  }, [sideNavContext, setter])
  return <SideNavContext.Provider value={valueObject}>{children}</SideNavContext.Provider>
}

export function useSideNavContext() {
  const context = useContext(SideNavContext)
  if (!context) {
    throw new Error('SideNavContext must be used within a SideNavContextProvider')
  }
  return context
}
