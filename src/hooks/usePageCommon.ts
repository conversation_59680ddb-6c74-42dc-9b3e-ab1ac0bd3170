import { useEffect, useState } from 'react'

import { useAxios } from './useAxios'
import { useAppDataContext } from '../contexts/AppDataContext'
import { useUdoContext } from '../contexts/UdoContext'
import { setNewRelicCustomAttributes } from '../utils/newrelic-util'
import { setUtagData } from '../utils/utag-util'

/*
usePageCommon is the externalized and exported hook. It is responsible for making each
app page work for both VIEWS from a single definition: The UI designer's view and the 
Spring java controller view that is ultimately seen by the end user. 
It takes the appName as input.
*/
export const usePageCommon = (appName = '', prefix = '') => {
  const { setAppData } = useAppDataContext()
  const { setUdoData } = useUdoContext()
  const initialState = {
    page: {
      custom: {},
      template: {
        displayHeader: false,
        displayFooter: false,
        sections: {},
        displayChat: false,
        chatContent: '',
        minimalFooter: false,
        minimalHeader: false
      },
      reserved: {
        csrfToken: '',
        pageversion: ''
      }
    },
    udo: {},
    prefix: prefix,
    shouldRender: false,
    appName: appName
  }
  const [pageData, setPageData] = useState(initialState)
  const isStorybook = appName === 'storybook'
  const isAEM = appName === 'aem-cms-react'
  const defaultPath = window.location.pathname === `${prefix}/${appName}`
  const handleOnComplete = (data: any) => {
    const { page = {}, udo = {} } = data
    updatePageData(page, udo)
  }

  const updatePageData = (page: any, udo: any) => {
    setPageData({ ...pageData, page: JSON.parse(JSON.stringify(page)), udo: JSON.parse(JSON.stringify(udo)) })
    setAppData({
      page: JSON.parse(JSON.stringify(page)),
      udo: JSON.parse(JSON.stringify(udo)),
      prefix: prefix,
      appName: appName,
      success: true
    })
    setUtagData(udo, page)
    setUdoData(udo)
    setNewRelicCustomAttributes()
  }

  const { axiosAPI } = useAxios({
    autoFetch: false,
    onCompleted: handleOnComplete
  })

  const getJsonData = async () => {
    await axiosAPI({ url: getApiURL(appName) })
  }

  useEffect(() => {
    if (isAEM) return
    else if (window?.RequestJson !== undefined && prefix === '') {
      updatePageData(window?.RequestJson?.page, window?.RequestJson?.udo)
    } else if (!defaultPath) {
      getJsonData()
    } else {
      updatePageData({}, {})
    }
  }, [])

  const sections = pageData?.page?.template?.sections || (pageData?.page as any)?.sections
  const displayHeader = pageData?.page?.template?.displayHeader || (pageData?.page as any)?.displayHeader || isStorybook
  const displayFooter = pageData?.page?.template?.displayFooter || (pageData?.page as any)?.displayFooter || isStorybook
  const minimalHeader = pageData?.page?.template?.minimalHeader || (pageData?.page as any)?.minimalHeader
  const minimalFooter = pageData?.page?.template?.minimalFooter || (pageData?.page as any)?.minimalFooter
  const displayChat = pageData?.page?.template?.displayChat || (pageData?.page as any)?.displayChat || isStorybook
  const chatContent =
    pageData?.page?.template?.chatContent || (pageData?.page as any)?.chatContent || isStorybook ? 'sales' : ''
  const shouldRender = defaultPath || (sections && Object.keys(sections).length > 0) || isStorybook || isAEM

  return {
    ...pageData,
    displayHeader,
    displayFooter,
    displayChat,
    chatContent,
    shouldRender,
    minimalHeader,
    minimalFooter
  }
}

/* 
pathname is used to derive the location of the .json contract for the page
This function will return the proper path name and filter out the trailing '-v8' 
used by a replacement page when it exists.
*/
const getPathName = (appName = '') => {
  const protoversion = getProtoVersion()
  let retval = String(window.location.pathname)
  if (retval.endsWith('-v8')) {
    retval = retval.substring(0, retval.length - 3)
  }
  return protoversion !== '' ? retval.replace(appName + '/', appName + '/protoversion/') : retval
}

const getProtoVersion = () => {
  const protoversion = new URL(document.location.toString()).searchParams.get('protoversion')
  return protoversion == null ? '' : '-' + protoversion
}

const getApiURL = (appName: string) => {
  let apiUrl = `${window.location.origin}${getPathName(appName)}${getProtoVersion()}.json`
  apiUrl = apiUrl.replace('-v8', '')
  return apiUrl
}
