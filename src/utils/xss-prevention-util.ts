/**
 * XSS Prevention Utilities
 *
 * This module provides utility functions to prevent Cross-Site Scripting (XSS) attacks
 * by sanitizing user inputs, URLs, and HTML attributes.
 */

import DOMPurify from 'dompurify'

/**
 * List of allowed URL protocols to prevent javascript: and data: URL attacks
 */
const ALLOWED_PROTOCOLS = ['http:', 'https:', 'mailto:', 'tel:', 'ftp:', 'ftps:']

/**
 * List of dangerous protocols that should be blocked
 */
const DANGEROUS_PROTOCOLS = ['javascript:', 'data:', 'vbscript:', 'file:', 'about:']

/**
 * Sanitizes a URL to prevent XSS attacks through href attributes
 * @param url - The URL to sanitize
 * @returns Sanitized URL or '#' if the URL is dangerous
 */
export const sanitizeUrl = (url: string | undefined | null): string => {
  if (!url || typeof url !== 'string') {
    return '#'
  }

  // Trim whitespace and convert to lowercase for protocol checking
  const trimmedUrl = url.trim()
  const lowerUrl = trimmedUrl.toLowerCase()

  // Check for dangerous protocols
  for (const protocol of DANGEROUS_PROTOCOLS) {
    if (lowerUrl.startsWith(protocol)) {
      console.warn(`Blocked dangerous URL protocol: ${protocol}`)
      return '#'
    }
  }

  // Allow relative URLs (starting with /, #, or ?)
  if (trimmedUrl.startsWith('/') || trimmedUrl.startsWith('#') || trimmedUrl.startsWith('?')) {
    return trimmedUrl
  }

  // Check if URL has a protocol
  try {
    const urlObj = new URL(trimmedUrl)
    if (ALLOWED_PROTOCOLS.includes(urlObj.protocol)) {
      return trimmedUrl
    } else {
      console.warn(`Blocked URL with disallowed protocol: ${urlObj.protocol}`)
      return '#'
    }
  } catch (error) {
    // If URL parsing fails, treat as relative URL if it doesn't contain dangerous patterns
    if (lowerUrl.includes('javascript:') || lowerUrl.includes('data:') || lowerUrl.includes('vbscript:')) {
      console.warn('Blocked URL with dangerous patterns')
      return '#'
    }
    return trimmedUrl
  }
}

/**
 * Sanitizes HTML attributes to prevent XSS attacks
 * @param value - The attribute value to sanitize
 * @returns Sanitized attribute value
 */
export const sanitizeAttribute = (value: string | undefined | null): string => {
  if (!value || typeof value !== 'string') {
    return ''
  }

  // Remove dangerous characters and patterns
  return value
    .replace(/[<>'"]/g, '') // Remove HTML special characters
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/data:/gi, '') // Remove data: protocol
    .replace(/vbscript:/gi, '') // Remove vbscript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers like onclick=
    .trim()
}

/**
 * Sanitizes text content to prevent XSS attacks
 * @param text - The text to sanitize
 * @returns Sanitized text with HTML entities encoded
 */
export const sanitizeText = (text: string | undefined | null): string => {
  if (!text || typeof text !== 'string') {
    return ''
  }

  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
}

/**
 * Validates and sanitizes an anchor/hash fragment
 * @param anchor - The anchor fragment to validate
 * @returns Sanitized anchor or empty string if invalid
 */
export const sanitizeAnchor = (anchor: string | undefined | null): string => {
  if (!anchor || typeof anchor !== 'string') {
    return ''
  }

  // Only allow alphanumeric characters, hyphens, and underscores in anchors
  const sanitized = anchor.replace(/[^a-zA-Z0-9\-_]/g, '')
  
  if (sanitized !== anchor) {
    console.warn('Anchor contained invalid characters and was sanitized')
  }

  return sanitized
}

/**
 * Validates if a URL is safe for use in href attributes
 * @param url - The URL to validate
 * @returns True if the URL is safe, false otherwise
 */
export const isUrlSafe = (url: string | undefined | null): boolean => {
  if (!url || typeof url !== 'string') {
    return false
  }

  const sanitized = sanitizeUrl(url)
  return sanitized !== '#' && sanitized === url
}

/**
 * Creates a safe href value by combining URL and anchor
 * @param baseUrl - The base URL
 * @param anchor - The anchor fragment
 * @returns Safe href value
 */
export const createSafeHref = (baseUrl: string | undefined | null, anchor?: string | undefined | null): string => {
  const safeUrl = sanitizeUrl(baseUrl)
  const safeAnchor = anchor ? sanitizeAnchor(anchor) : ''

  if (safeUrl === '#') {
    return '#'
  }

  if (safeAnchor) {
    // If URL already has a hash, replace it; otherwise append
    const urlWithoutHash = safeUrl.split('#')[0]
    return `${urlWithoutHash}#${safeAnchor}`
  }

  return safeUrl
}

/**
 * Sanitizes form action URLs
 * @param action - The form action URL
 * @returns Sanitized action URL
 */
export const sanitizeFormAction = (action: string | undefined | null): string => {
  const sanitized = sanitizeUrl(action)

  // For form actions, we're more restrictive - only allow relative URLs and HTTPS
  if (sanitized === '#') {
    return ''
  }

  // Allow relative URLs
  if (sanitized.startsWith('/') || sanitized.startsWith('?')) {
    return sanitized
  }

  // Only allow HTTPS for absolute URLs in form actions
  try {
    const urlObj = new URL(sanitized)
    if (urlObj.protocol === 'https:') {
      return sanitized
    } else {
      console.warn('Form action must use HTTPS for absolute URLs')
      return ''
    }
  } catch (error) {
    return ''
  }
}

/**
 * Sanitizes HTML content to prevent XSS attacks while preserving safe HTML
 * @param html - The HTML content to sanitize
 * @param allowedTags - Optional array of allowed HTML tags
 * @returns Sanitized HTML content
 */
export const sanitizeHtml = (
  html: string | undefined | null,
  allowedTags?: string[]
): string => {
  if (!html || typeof html !== 'string') {
    return ''
  }

  // Configure DOMPurify options
  const config: DOMPurify.Config = {
    // Allow common safe HTML tags by default
    ALLOWED_TAGS: allowedTags || [
      'p', 'br', 'strong', 'em', 'u', 'i', 'b',
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'ul', 'ol', 'li',
      'a', 'img',
      'div', 'span',
      'blockquote', 'pre', 'code'
    ],
    // Allow safe attributes
    ALLOWED_ATTR: [
      'href', 'src', 'alt', 'title', 'class', 'id',
      'target', 'rel', 'aria-label', 'aria-describedby'
    ],
    // Additional security options
    ALLOW_DATA_ATTR: false,
    ALLOW_UNKNOWN_PROTOCOLS: false,
    SANITIZE_DOM: true,
    KEEP_CONTENT: true
  }

  try {
    return DOMPurify.sanitize(html, config)
  } catch (error) {
    console.error('HTML sanitization failed:', error)
    return ''
  }
}

/**
 * Sanitizes HTML content for rich text components with more permissive settings
 * @param html - The HTML content to sanitize
 * @returns Sanitized HTML content suitable for rich text display
 */
export const sanitizeRichTextHtml = (html: string | undefined | null): string => {
  if (!html || typeof html !== 'string') {
    return ''
  }

  const config: DOMPurify.Config = {
    // More permissive tag list for rich text content
    ALLOWED_TAGS: [
      'p', 'br', 'strong', 'em', 'u', 'i', 'b', 's', 'sub', 'sup',
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'ul', 'ol', 'li', 'dl', 'dt', 'dd',
      'a', 'img', 'figure', 'figcaption',
      'div', 'span', 'section', 'article',
      'blockquote', 'pre', 'code',
      'table', 'thead', 'tbody', 'tr', 'th', 'td',
      'hr'
    ],
    ALLOWED_ATTR: [
      'href', 'src', 'alt', 'title', 'class', 'id',
      'target', 'rel', 'aria-label', 'aria-describedby',
      'width', 'height', 'style'
    ],
    ALLOW_DATA_ATTR: false,
    ALLOW_UNKNOWN_PROTOCOLS: false,
    SANITIZE_DOM: true,
    KEEP_CONTENT: true
  }

  try {
    let sanitized = DOMPurify.sanitize(html, config)

    // Post-process to sanitize URLs in href and src attributes
    if (sanitized && typeof sanitized === 'string') {
      // Replace dangerous URLs in href attributes
      sanitized = sanitized.replace(/href="([^"]*)"/g, (_, url) => {
        const safeUrl = sanitizeUrl(url)
        return `href="${safeUrl}"`
      })

      // Replace dangerous URLs in src attributes
      sanitized = sanitized.replace(/src="([^"]*)"/g, (_, url) => {
        const safeUrl = sanitizeUrl(url)
        return `src="${safeUrl}"`
      })
    }

    return sanitized
  } catch (error) {
    console.error('Rich text HTML sanitization failed:', error)
    return ''
  }
}
