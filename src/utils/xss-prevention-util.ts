/**
 * XSS Prevention Utilities
 * 
 * This module provides utility functions to prevent Cross-Site Scripting (XSS) attacks
 * by sanitizing user inputs, URLs, and HTML attributes.
 */

/**
 * List of allowed URL protocols to prevent javascript: and data: URL attacks
 */
const ALLOWED_PROTOCOLS = ['http:', 'https:', 'mailto:', 'tel:', 'ftp:', 'ftps:']

/**
 * List of dangerous protocols that should be blocked
 */
const DANGEROUS_PROTOCOLS = ['javascript:', 'data:', 'vbscript:', 'file:', 'about:']

/**
 * Sanitizes a URL to prevent XSS attacks through href attributes
 * @param url - The URL to sanitize
 * @returns Sanitized URL or '#' if the URL is dangerous
 */
export const sanitizeUrl = (url: string | undefined | null): string => {
  if (!url || typeof url !== 'string') {
    return '#'
  }

  // Trim whitespace and convert to lowercase for protocol checking
  const trimmedUrl = url.trim()
  const lowerUrl = trimmedUrl.toLowerCase()

  // Check for dangerous protocols
  for (const protocol of DANGEROUS_PROTOCOLS) {
    if (lowerUrl.startsWith(protocol)) {
      console.warn(`Blocked dangerous URL protocol: ${protocol}`)
      return '#'
    }
  }

  // Allow relative URLs (starting with /, #, or ?)
  if (trimmedUrl.startsWith('/') || trimmedUrl.startsWith('#') || trimmedUrl.startsWith('?')) {
    return trimmedUrl
  }

  // Check if URL has a protocol
  try {
    const urlObj = new URL(trimmedUrl)
    if (ALLOWED_PROTOCOLS.includes(urlObj.protocol)) {
      return trimmedUrl
    } else {
      console.warn(`Blocked URL with disallowed protocol: ${urlObj.protocol}`)
      return '#'
    }
  } catch (error) {
    // If URL parsing fails, treat as relative URL if it doesn't contain dangerous patterns
    if (lowerUrl.includes('javascript:') || lowerUrl.includes('data:') || lowerUrl.includes('vbscript:')) {
      console.warn('Blocked URL with dangerous patterns')
      return '#'
    }
    return trimmedUrl
  }
}

/**
 * Sanitizes HTML attributes to prevent XSS attacks
 * @param value - The attribute value to sanitize
 * @returns Sanitized attribute value
 */
export const sanitizeAttribute = (value: string | undefined | null): string => {
  if (!value || typeof value !== 'string') {
    return ''
  }

  // Remove dangerous characters and patterns
  return value
    .replace(/[<>'"]/g, '') // Remove HTML special characters
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/data:/gi, '') // Remove data: protocol
    .replace(/vbscript:/gi, '') // Remove vbscript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers like onclick=
    .trim()
}

/**
 * Sanitizes text content to prevent XSS attacks
 * @param text - The text to sanitize
 * @returns Sanitized text with HTML entities encoded
 */
export const sanitizeText = (text: string | undefined | null): string => {
  if (!text || typeof text !== 'string') {
    return ''
  }

  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
}

/**
 * Validates and sanitizes an anchor/hash fragment
 * @param anchor - The anchor fragment to validate
 * @returns Sanitized anchor or empty string if invalid
 */
export const sanitizeAnchor = (anchor: string | undefined | null): string => {
  if (!anchor || typeof anchor !== 'string') {
    return ''
  }

  // Only allow alphanumeric characters, hyphens, and underscores in anchors
  const sanitized = anchor.replace(/[^a-zA-Z0-9\-_]/g, '')
  
  if (sanitized !== anchor) {
    console.warn('Anchor contained invalid characters and was sanitized')
  }

  return sanitized
}

/**
 * Validates if a URL is safe for use in href attributes
 * @param url - The URL to validate
 * @returns True if the URL is safe, false otherwise
 */
export const isUrlSafe = (url: string | undefined | null): boolean => {
  if (!url || typeof url !== 'string') {
    return false
  }

  const sanitized = sanitizeUrl(url)
  return sanitized !== '#' && sanitized === url
}

/**
 * Creates a safe href value by combining URL and anchor
 * @param baseUrl - The base URL
 * @param anchor - The anchor fragment
 * @returns Safe href value
 */
export const createSafeHref = (baseUrl: string | undefined | null, anchor?: string | undefined | null): string => {
  const safeUrl = sanitizeUrl(baseUrl)
  const safeAnchor = anchor ? sanitizeAnchor(anchor) : ''

  if (safeUrl === '#') {
    return '#'
  }

  if (safeAnchor) {
    // If URL already has a hash, replace it; otherwise append
    const urlWithoutHash = safeUrl.split('#')[0]
    return `${urlWithoutHash}#${safeAnchor}`
  }

  return safeUrl
}

/**
 * Sanitizes form action URLs
 * @param action - The form action URL
 * @returns Sanitized action URL
 */
export const sanitizeFormAction = (action: string | undefined | null): string => {
  const sanitized = sanitizeUrl(action)
  
  // For form actions, we're more restrictive - only allow relative URLs and HTTPS
  if (sanitized === '#') {
    return ''
  }

  // Allow relative URLs
  if (sanitized.startsWith('/') || sanitized.startsWith('?')) {
    return sanitized
  }

  // Only allow HTTPS for absolute URLs in form actions
  try {
    const urlObj = new URL(sanitized)
    if (urlObj.protocol === 'https:') {
      return sanitized
    } else {
      console.warn('Form action must use HTTPS for absolute URLs')
      return ''
    }
  } catch (error) {
    return ''
  }
}
