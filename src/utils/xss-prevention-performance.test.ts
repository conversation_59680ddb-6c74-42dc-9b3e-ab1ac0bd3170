/**
 * Performance tests for XSS prevention utilities
 * Run with: pnpm test src/utils/xss-prevention-performance.test.ts
 */

import {
  sanitizeUrl,
  sanitizeHtml,
  sanitizeRichTextHtml,
  sanitizeAttribute,
  sanitizeText
} from './xss-prevention-util'

describe('XSS Prevention Performance Tests', () => {
  const measurePerformance = (fn: () => void, iterations = 1000): number => {
    const start = performance.now()
    for (let i = 0; i < iterations; i++) {
      fn()
    }
    const end = performance.now()
    return (end - start) / iterations // Average time per operation in ms
  }

  describe('URL Sanitization Performance', () => {
    it('should sanitize URLs quickly', () => {
      const testUrls = [
        'https://example.com',
        '/relative/path',
        'javascript:alert(1)',
        'mailto:<EMAIL>',
        '#anchor'
      ]

      testUrls.forEach(url => {
        const avgTime = measurePerformance(() => sanitizeUrl(url), 10000)
        console.log(`URL "${url}": ${avgTime.toFixed(4)}ms avg`)
        expect(avgTime).toBeLessThan(0.1) // Should be under 0.1ms per URL
      })
    })
  })

  describe('HTML Sanitization Performance', () => {
    it('should sanitize small HTML quickly', () => {
      const smallHtml = '<p>Hello <strong>world</strong></p>'
      
      const avgTime = measurePerformance(() => sanitizeHtml(smallHtml), 1000)
      console.log(`Small HTML: ${avgTime.toFixed(4)}ms avg`)
      expect(avgTime).toBeLessThan(2) // Should be under 2ms for small HTML
    })

    it('should handle medium HTML reasonably', () => {
      const mediumHtml = `
        <div class="content">
          <h1>Title</h1>
          <p>This is a <strong>test</strong> with <a href="https://example.com">links</a></p>
          <ul>
            <li>Item 1</li>
            <li>Item 2</li>
            <li>Item 3</li>
          </ul>
          <blockquote>Quote content</blockquote>
        </div>
      `
      
      const avgTime = measurePerformance(() => sanitizeRichTextHtml(mediumHtml), 1000)
      console.log(`Medium HTML: ${avgTime.toFixed(4)}ms avg`)
      expect(avgTime).toBeLessThan(5) // Should be under 5ms for medium HTML
    })

    it('should handle large HTML acceptably', () => {
      // Generate large HTML content
      const largeHtml = Array(100).fill(0).map((_, i) => 
        `<div><h2>Section ${i}</h2><p>Content with <strong>formatting</strong> and <a href="/link${i}">links</a></p></div>`
      ).join('')
      
      const avgTime = measurePerformance(() => sanitizeRichTextHtml(largeHtml), 100)
      console.log(`Large HTML: ${avgTime.toFixed(4)}ms avg`)
      expect(avgTime).toBeLessThan(20) // Should be under 20ms for large HTML
    })
  })

  describe('Attribute Sanitization Performance', () => {
    it('should sanitize attributes very quickly', () => {
      const testAttributes = [
        'normal-value',
        'onclick=alert(1)',
        'javascript:alert(1)',
        'value with spaces',
        'complex<script>alert(1)</script>value'
      ]

      testAttributes.forEach(attr => {
        const avgTime = measurePerformance(() => sanitizeAttribute(attr), 10000)
        console.log(`Attribute "${attr}": ${avgTime.toFixed(4)}ms avg`)
        expect(avgTime).toBeLessThan(0.05) // Should be under 0.05ms per attribute
      })
    })
  })

  describe('Text Sanitization Performance', () => {
    it('should sanitize text very quickly', () => {
      const testTexts = [
        'Normal text',
        'Text with <script>alert(1)</script>',
        'Text with "quotes" and \'apostrophes\'',
        'Text & symbols < > / etc'
      ]

      testTexts.forEach(text => {
        const avgTime = measurePerformance(() => sanitizeText(text), 10000)
        console.log(`Text "${text}": ${avgTime.toFixed(4)}ms avg`)
        expect(avgTime).toBeLessThan(0.02) // Should be under 0.02ms per text
      })
    })
  })

  describe('Real-world Scenarios', () => {
    it('should handle typical component rendering quickly', () => {
      // Simulate a typical component with multiple sanitization calls
      const simulateComponentRender = () => {
        // Typical BaseButton component
        sanitizeUrl('https://example.com/action')
        sanitizeUrl('/relative/path')
        
        // Typical RichText component
        sanitizeRichTextHtml('<p>Welcome to our <strong>website</strong>! <a href="https://example.com">Learn more</a></p>')
        
        // Typical form attributes
        sanitizeAttribute('btn-primary')
        sanitizeAttribute('Submit Form')
      }

      const avgTime = measurePerformance(simulateComponentRender, 1000)
      console.log(`Component render simulation: ${avgTime.toFixed(4)}ms avg`)
      expect(avgTime).toBeLessThan(3) // Should be under 3ms for typical component
    })

    it('should handle page with multiple components efficiently', () => {
      // Simulate a page with 20 components
      const simulatePageRender = () => {
        for (let i = 0; i < 20; i++) {
          sanitizeUrl(`/page/${i}`)
          sanitizeRichTextHtml(`<h2>Component ${i}</h2><p>Content for component ${i}</p>`)
          sanitizeAttribute(`component-${i}`)
        }
      }

      const avgTime = measurePerformance(simulatePageRender, 100)
      console.log(`Page render simulation (20 components): ${avgTime.toFixed(4)}ms avg`)
      expect(avgTime).toBeLessThan(50) // Should be under 50ms for 20 components
    })
  })

  describe('Memory Usage', () => {
    it('should not cause memory leaks', () => {
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0
      
      // Perform many sanitization operations
      for (let i = 0; i < 10000; i++) {
        sanitizeUrl(`https://example.com/${i}`)
        sanitizeHtml(`<p>Test content ${i}</p>`)
        sanitizeAttribute(`value-${i}`)
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc()
      }

      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0
      const memoryIncrease = finalMemory - initialMemory

      console.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`)
      
      // Memory increase should be reasonable (less than 10MB for 10k operations)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024)
    })
  })
})
