import {
  sanitizeUrl,
  sanitizeAttribute,
  sanitizeText,
  sanitizeAnchor,
  isUrlSafe,
  createSafeHref,
  sanitizeFormAction
} from './xss-prevention-util'

describe('XSS Prevention Utilities', () => {
  describe('sanitizeUrl', () => {
    it('should allow safe HTTP and HTTPS URLs', () => {
      expect(sanitizeUrl('https://example.com')).toBe('https://example.com')
      expect(sanitizeUrl('http://example.com')).toBe('http://example.com')
    })

    it('should allow relative URLs', () => {
      expect(sanitizeUrl('/path/to/page')).toBe('/path/to/page')
      expect(sanitizeUrl('#anchor')).toBe('#anchor')
      expect(sanitizeUrl('?query=value')).toBe('?query=value')
    })

    it('should block javascript: URLs', () => {
      expect(sanitizeUrl('javascript:alert("xss")')).toBe('#')
      expect(sanitizeUrl('JAVASCRIPT:alert("xss")')).toBe('#')
    })

    it('should block data: URLs', () => {
      expect(sanitizeUrl('data:text/html,<script>alert("xss")</script>')).toBe('#')
    })

    it('should block vbscript: URLs', () => {
      expect(sanitizeUrl('vbscript:alert("xss")')).toBe('#')
    })

    it('should handle null and undefined inputs', () => {
      expect(sanitizeUrl(null)).toBe('#')
      expect(sanitizeUrl(undefined)).toBe('#')
      expect(sanitizeUrl('')).toBe('#')
    })

    it('should allow mailto and tel URLs', () => {
      expect(sanitizeUrl('mailto:<EMAIL>')).toBe('mailto:<EMAIL>')
      expect(sanitizeUrl('tel:+1234567890')).toBe('tel:+1234567890')
    })
  })

  describe('sanitizeAttribute', () => {
    it('should remove HTML special characters', () => {
      expect(sanitizeAttribute('value<script>')).toBe('valuescript')
      expect(sanitizeAttribute('value"test')).toBe('valuetest')
      expect(sanitizeAttribute("value'test")).toBe('valuetest')
    })

    it('should remove dangerous protocols', () => {
      expect(sanitizeAttribute('javascript:alert(1)')).toBe('alert(1)')
      expect(sanitizeAttribute('data:text/html')).toBe('text/html')
    })

    it('should remove event handlers', () => {
      expect(sanitizeAttribute('onclick=alert(1)')).toBe('')
      expect(sanitizeAttribute('onload=malicious()')).toBe('')
    })

    it('should handle null and undefined inputs', () => {
      expect(sanitizeAttribute(null)).toBe('')
      expect(sanitizeAttribute(undefined)).toBe('')
    })
  })

  describe('sanitizeText', () => {
    it('should encode HTML entities', () => {
      expect(sanitizeText('<script>alert("xss")</script>')).toBe('&lt;script&gt;alert(&quot;xss&quot;)&lt;&#x2F;script&gt;')
    })

    it('should encode quotes and ampersands', () => {
      expect(sanitizeText('Tom & Jerry "cartoon"')).toBe('Tom &amp; Jerry &quot;cartoon&quot;')
    })

    it('should handle null and undefined inputs', () => {
      expect(sanitizeText(null)).toBe('')
      expect(sanitizeText(undefined)).toBe('')
    })
  })

  describe('sanitizeAnchor', () => {
    it('should allow valid anchor characters', () => {
      expect(sanitizeAnchor('valid-anchor_123')).toBe('valid-anchor_123')
    })

    it('should remove invalid characters', () => {
      expect(sanitizeAnchor('invalid<>anchor')).toBe('invalidanchor')
      expect(sanitizeAnchor('anchor with spaces')).toBe('anchorwithspaces')
    })

    it('should handle null and undefined inputs', () => {
      expect(sanitizeAnchor(null)).toBe('')
      expect(sanitizeAnchor(undefined)).toBe('')
    })
  })

  describe('isUrlSafe', () => {
    it('should return true for safe URLs', () => {
      expect(isUrlSafe('https://example.com')).toBe(true)
      expect(isUrlSafe('/relative/path')).toBe(true)
    })

    it('should return false for dangerous URLs', () => {
      expect(isUrlSafe('javascript:alert(1)')).toBe(false)
      expect(isUrlSafe('data:text/html')).toBe(false)
    })

    it('should return false for null and undefined', () => {
      expect(isUrlSafe(null)).toBe(false)
      expect(isUrlSafe(undefined)).toBe(false)
    })
  })

  describe('createSafeHref', () => {
    it('should combine URL and anchor safely', () => {
      expect(createSafeHref('https://example.com', 'section1')).toBe('https://example.com#section1')
      expect(createSafeHref('/page', 'anchor')).toBe('/page#anchor')
    })

    it('should handle dangerous URLs', () => {
      expect(createSafeHref('javascript:alert(1)', 'anchor')).toBe('#')
    })

    it('should sanitize anchors', () => {
      expect(createSafeHref('/page', 'anchor<script>')).toBe('/page#anchorscript')
    })

    it('should replace existing anchors', () => {
      expect(createSafeHref('/page#old', 'new')).toBe('/page#new')
    })
  })

  describe('sanitizeFormAction', () => {
    it('should allow relative URLs', () => {
      expect(sanitizeFormAction('/submit')).toBe('/submit')
      expect(sanitizeFormAction('?action=submit')).toBe('?action=submit')
    })

    it('should allow HTTPS URLs', () => {
      expect(sanitizeFormAction('https://api.example.com/submit')).toBe('https://api.example.com/submit')
    })

    it('should block HTTP URLs for forms', () => {
      expect(sanitizeFormAction('http://example.com/submit')).toBe('')
    })

    it('should block dangerous URLs', () => {
      expect(sanitizeFormAction('javascript:alert(1)')).toBe('')
      expect(sanitizeFormAction('data:text/html')).toBe('')
    })
  })
})
