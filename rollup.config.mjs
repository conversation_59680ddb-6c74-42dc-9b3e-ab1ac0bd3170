import dotenv from 'dotenv'
dotenv.config()

import babel from '@rollup/plugin-babel'
import commonjs from '@rollup/plugin-commonjs'
import json from '@rollup/plugin-json'
import resolve from '@rollup/plugin-node-resolve'
import replace from '@rollup/plugin-replace'
import terser from '@rollup/plugin-terser'
import typescript from '@rollup/plugin-typescript'
import { createRequire } from 'module'
import dts from 'rollup-plugin-dts'
import peerDepsExternal from 'rollup-plugin-peer-deps-external'
import postcss from 'rollup-plugin-postcss'
import { visualizer } from 'rollup-plugin-visualizer'

const sourceMap = false

const require = createRequire(import.meta.url)
const pkg = require('./package.json')

export default [
  {
    input: 'src/index.ts',
    external: ['react', 'react-dom', 'styled-components'],
    // external: [
    //   ...Object.keys(pkg.dependencies),
    //   ...Object.keys(pkg.peerDependencies),
    //   'styled-components'
    // ],
    output: [
      {
        file: pkg.main,
        format: 'cjs',
        sourcemap: sourceMap
      },
      {
        file: pkg.module,
        format: 'esm',
        sourcemap: sourceMap
      }
    ],
    plugins: [
      peerDepsExternal(),
      replace({
        preventAssignment: true,
        'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'production'),
        'process.env.YEXT_API_KEY': JSON.stringify(process.env.YEXT_API_KEY || '')
      }),
      resolve({ browser: true }),
      commonjs(),
      typescript({
        tsconfig: './tsconfig.json',
        sourceMap: sourceMap,
        exclude: ['**/*.test.ts', '**/*.test.tsx', '**/*.stories.ts', '**/*.stories.tsx']
      }),
      postcss({ extract: true, minimize: true }),
      terser(),
      json()
    ]
  },
  {
    input: {
      /* aem*/
      AemRenderer: 'src/components/aem/AemRenderer/index.ts',
      ColumnLayout: 'src/components/aem/ColumnLayout/index.ts',
      RuleBuilder: 'src/components/aem/RuleBuilder/index.ts',
      RuleBuilderConditional: 'src/components/aem/RuleBuilderConditional/index.ts',
      /* atoms */
      Accordion: 'src/components/atoms/Accordion/index.ts',
      AutocompleteInput: 'src/components/atoms/AutocompleteInput/index.ts',
      Badge: 'src/components/atoms/Badge/index.ts',
      Banner: 'src/components/atoms/Banner/index.ts',
      Button: 'src/components/atoms/Button/index.ts',
      ButtonGroup: 'src/components/atoms/ButtonGroup/index.ts',
      Image: 'src/components/atoms/Image/index.ts',
      Modal: 'src/components/atoms/Modal/index.ts',
      ModalBody: 'src/components/atoms/Modal/ModalBody/index.ts',
      ModalHeader: 'src/components/atoms/Modal/ModalHeader/index.ts',
      ModalFooter: 'src/components/atoms/Modal/ModalFooter/index.ts',
      Pagination: 'src/components/atoms/Pagination/index.ts',
      RichText: 'src/components/atoms/RichText/index.ts',
      SectionHeader: 'src/components/atoms/SectionHeader/index.ts',
      Separator: 'src/components/atoms/Separator/index.ts',
      Skeleton: 'src/components/atoms/Skeleton/index.ts',
      Spinner: 'src/components/atoms/Spinner/index.ts',
      StoreButton: 'src/components/atoms/StoreButton/index.ts',
      Toggle: 'src/components/atoms/Toggle/index.ts',
      Typography: 'src/components/atoms/Typography/index.ts',
      YextSearch: 'src/components/atoms/YextSearch/index.ts',
      /*molecules*/
      BBFL: 'src/components/molecules/BBFL/index.ts',
      GlobalNav: 'src/components/molecules/GlobalNav/index.ts',
      SolutionList: 'src/components/molecules/SolutionList/index.ts',
      /*organisms*/
      Footer: 'src/components/organisms/Footer/index.ts',
      FormCheckbox: 'src/components/organisms/Form/FormCheckbox/index.ts',
      FormContainer: 'src/components/organisms/Form/FormContainer/index.ts',
      FormDropdown: 'src/components/organisms/Form/FormDropdown/index.ts',
      FormInput: 'src/components/organisms/Form/FormInput/index.ts',
      FormRadioButton: 'src/components/organisms/Form/FormRadioButton/index.ts',
      FormToggle: 'src/components/organisms/Form/FormToggle/index.ts',
      FormChecklist: 'src/components/organisms/Form/shared/FormChecklist/index.ts',
      FormLabel: 'src/components/organisms/Form/shared/FormLabel/index.ts',
      FormMessage: 'src/components/organisms/Form/shared/FormMessage/index.ts',
      Header: 'src/components/organisms/Header/index.ts',
      Form: 'src/components/organisms/Form/index.ts',
      /*templates*/
      ErrorBoundary: 'src/components/templates/ErrorBoundary/index.ts',
      Fallback: 'src/components/templates/Fallback/index.ts',
      GlobalSideNav: 'src/components/templates/GlobalSideNav/index.ts',
      Layout: 'src/components/templates/Layout/index.ts',
      PageLoader: 'src/components/templates/PageLoader/index.ts',
      /**Contexts */
      AddressContext: 'src/contexts/AddressContext.tsx',
      AppDataContext: 'src/contexts/AppDataContext.tsx',
      BBFLContext: 'src/contexts/BBFLContext.tsx',
      FlexOffersContext: 'src/contexts/FlexOffersContext.tsx',
      FormContext: 'src/contexts/FormContext.tsx',
      HeaderProfileContext: 'src/contexts/HeaderProfileContext.tsx',
      LocationContext: 'src/contexts/LocationContext.tsx',
      SideNavContext: 'src/contexts/SideNavContext.tsx',
      UdoContext: 'src/contexts/UdoContext.tsx',
      /**Hooks */
      useAxios: 'src/hooks/useAxios.ts',
      useClickAwayListener: 'src/hooks/useClickAwayListener.ts',
      useFlexOffers: 'src/hooks/useFlexOffers.tsx',
      useFormStatus: 'src/hooks/useFormStatus.ts',
      useLobExperience: 'src/hooks/useLobExperience.ts',
      useOliverChat: 'src/hooks/useOliverChat.ts',
      usePageCommon: 'src/hooks/usePageCommon.ts',
      usePageVersion: 'src/hooks/usePageVersion.ts',
      useWindowSize: 'src/hooks/useWindowSize.ts',
      /**Hoc */
      withAxios: 'src/hoc/withAxios/index.tsx',
      withTemplateData: 'src/hoc/withTemplateData/index.tsx',
      /**Types */
      types: 'src/types/index.ts'
    },
    external: ['react', 'react-dom', 'styled-components'],
    // external: [
    //   ...Object.keys(pkg.dependencies),
    //   ...Object.keys(pkg.peerDependencies),
    //   'styled-components'
    // ],
    output: [
      // {
      //   dir: 'dist',
      //   format: 'cjs',
      //   sourcemap: sourceMap,
      //   // entryFileNames: '[name].js',
      //   entryFileNames: '[name]/index.js',
      //   chunkFileNames: '[name]-[hash].js'
      // },
      {
        dir: 'dist',
        format: 'esm',
        sourcemap: sourceMap,
        // entryFileNames: '[name].js',
        entryFileNames: '[name]/index.js',
        chunkFileNames: '[name]/[name]-[hash].js'
      }
    ],
    plugins: [
      peerDepsExternal(),
      replace({
        preventAssignment: true,
        'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'production'),
        'process.env.YEXT_API_KEY': JSON.stringify(process.env.YEXT_API_KEY || '')
      }),
      resolve({ browser: true }),
      commonjs(),
      typescript({ tsconfig: './tsconfig.json', sourceMap: sourceMap }),
      postcss({ extract: true, minimize: true }),
      terser(),
      json(),
      visualizer({
        filename: 'bundle-stats.html',
        open: false
      })
    ]
  },
  {
    input: {
      /*aem */
      AemRenderer: 'dist/esm/types/components/aem/AemRenderer/index.d.ts',
      ColumnLayout: 'dist/esm/types/components/aem/ColumnLayout/index.d.ts',
      RuleBuilder: 'dist/esm/types/components/aem/RuleBuilder/index.d.ts',
      RuleBuilderConditional: 'dist/esm/types/components/aem/RuleBuilderConditional/index.d.ts',
      /* atoms */
      Accordion: 'dist/esm/types/components/atoms/Accordion/index.d.ts',
      AutocompleteInput: 'dist/esm/types/components/atoms/AutocompleteInput/index.d.ts',
      Badge: 'dist/esm/types/components/atoms/Badge/index.d.ts',
      Banner: 'dist/esm/types/components/atoms/Banner/index.d.ts',
      Button: 'dist/esm/types/components/atoms/Button/index.d.ts',
      ButtonGroup: 'dist/esm/types/components/atoms/ButtonGroup/index.d.ts',
      Image: 'dist/esm/types/components/atoms/Image/index.d.ts',
      Modal: 'dist/esm/types/components/atoms/Modal/index.d.ts',
      ModalBody: 'dist/esm/types/components/atoms/Modal/ModalBody/index.d.ts',
      ModalHeader: 'dist/esm/types/components/atoms/Modal/ModalHeader/index.d.ts',
      ModalFooter: 'dist/esm/types/components/atoms/Modal/ModalFooter/index.d.ts',
      Pagination: 'dist/esm/types/components/atoms/Pagination/index.d.ts',
      RichText: 'dist/esm/types/components/atoms/RichText/index.d.ts',
      SectionHeader: 'dist/esm/types/components/atoms/SectionHeader/index.d.ts',
      Separator: 'dist/esm/types/components/atoms/Separator/index.d.ts',
      Skeleton: 'dist/esm/types/components/atoms/Skeleton/index.d.ts',
      Spinner: 'dist/esm/types/components/atoms/Spinner/index.d.ts',
      StoreButton: 'dist/esm/types/components/atoms/StoreButton/index.d.ts',
      Toggle: 'dist/esm/types/components/atoms/Toggle/index.d.ts',
      Typography: 'dist/esm/types/components/atoms/Typography/index.d.ts',
      YextSearch: 'dist/esm/types/components/atoms/YextSearch/index.d.ts',
      /*molecules*/
      BBFL: 'dist/esm/types/components/molecules/BBFL/index.d.ts',
      GlobalNav: 'dist/esm/types/components/molecules/GlobalNav/index.d.ts',
      SolutionList: 'dist/esm/types/components/molecules/SolutionList/index.d.ts',
      /*organisms*/
      Footer: 'dist/esm/types/components/organisms/Footer/index.d.ts',
      FormCheckbox: 'dist/esm/types/components/organisms/Form/FormCheckbox/index.d.ts',
      FormContainer: 'dist/esm/types/components/organisms/Form/FormContainer/index.d.ts',
      FormDropdown: 'dist/esm/types/components/organisms/Form/FormDropdown/index.d.ts',
      FormInput: 'dist/esm/types/components/organisms/Form/FormInput/index.d.ts',
      FormRadioButton: 'dist/esm/types/components/organisms/Form/FormRadioButton/index.d.ts',
      FormToggle: 'dist/esm/types/components/organisms/Form/FormToggle/index.d.ts',
      FormChecklist: 'dist/esm/types/components/organisms/Form/shared/FormChecklist/index.d.ts',
      FormLabel: 'dist/esm/types/components/organisms/Form/shared/FormLabel/index.d.ts',
      FormMessage: 'dist/esm/types/components/organisms/Form/shared/FormMessage/index.d.ts',
      Form: 'dist/esm/types/components/organisms/Form/index.d.ts',
      Header: 'dist/esm/types/components/organisms/Header/index.d.ts',
      /*templates*/
      ErrorBoundary: 'dist/esm/types/components/templates/ErrorBoundary/index.d.ts',
      Fallback: 'dist/esm/types/components/templates/Fallback/index.d.ts',
      GlobalSideNav: 'dist/esm/types/components/templates/GlobalSideNav/index.d.ts',
      Layout: 'dist/esm/types/components/templates/Layout/index.d.ts',
      PageLoader: 'dist/esm/types/components/templates/PageLoader/index.d.ts',
      /**Contexts */
      AddressContext: 'dist/esm/types/contexts/AddressContext.d.ts',
      AppDataContext: 'dist/esm/types/contexts/AppDataContext.d.ts',
      BBFLContext: 'dist/esm/types/contexts/BBFLContext.d.ts',
      FlexOffersContext: 'dist/esm/types/contexts/FlexOffersContext.d.ts',
      FormContext: 'dist/esm/types/contexts/FormContext.d.ts',
      HeaderProfileContext: 'dist/esm/types/contexts/HeaderProfileContext.d.ts',
      LocationContext: 'dist/esm/types/contexts/LocationContext.d.ts',
      SideNavContext: 'dist/esm/types/contexts/SideNavContext.d.ts',
      UdoContext: 'dist/esm/types/contexts/UdoContext.d.ts',
      /**Hooks */
      useAxios: 'dist/esm/types/hooks/useAxios.d.ts',
      useClickAwayListener: 'dist/esm/types/hooks/useClickAwayListener.d.ts',
      useFlexOffers: 'dist/esm/types/hooks/useFlexOffers.d.ts',
      useFormStatus: 'dist/esm/types/hooks/useFormStatus.d.ts',
      useLobExperience: 'dist/esm/types/hooks/useLobExperience.d.ts',
      useOliverChat: 'dist/esm/types/hooks/useOliverChat.d.ts',
      usePageCommon: 'dist/esm/types/hooks/usePageCommon.d.ts',
      usePageVersion: 'dist/esm/types/hooks/usePageVersion.d.ts',
      useWindowSize: 'dist/esm/types/hooks/useWindowSize.d.ts',
      /**Hoc */
      withAxios: 'dist/esm/types/hoc/withAxios/index.d.ts',
      withTemplateData: 'dist/esm/types/hoc/withTemplateData/index.d.ts',
      /**Types */
      types: 'dist/esm/types/types/index.d.ts'
    },
    output: [
      {
        dir: 'dist',
        format: 'esm',
        entryFileNames: '[name]/index.d.ts'
      }
    ],
    plugins: [dts()],
    external: [/\.(css|less|scss)$/]
  },
  {
    input: './src/JsWrapper.tsx',
    output: {
      file: 'dist/@cox/core-ui8.min.js',
      format: 'iife',
      name: 'UI8',
      sourcemap: sourceMap,
      globals: {
        react: 'React',
        'react-dom': 'ReactDOM',
        'rollup-react': 'rollupReact'
      }
    },
    plugins: [
      replace({
        preventAssignment: true,
        'process.browser': true,
        'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'production'),
        'process.env.YEXT_API_KEY': JSON.stringify(process.env.YEXT_API_KEY || '')
      }),
      peerDepsExternal(),
      resolve(),
      commonjs(),
      typescript({
        tsconfig: './tsconfig.json',
        sourceMap: sourceMap,
        resolveJsonModule: true
      }),
      postcss({ extract: true, minimize: true }),
      terser(),
      babel({
        exclude: 'node_modules/**',
        babelHelpers: 'bundled'
      }),
      json()
    ],
    external: ['react', 'react-dom', 'styled-components']
  },
  {
    input: 'dist/esm/types/index.d.ts',
    output: [{ file: 'dist/index.d.ts', format: 'esm' }],
    plugins: [dts()],
    external: [/\.(css|less|scss)$/]
  }
]
